"use client";

import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/24/solid";
import { motion } from "framer-motion";
import { useEffect, useRef, useState } from "react";
import Button from "./button";

type SliderProps = {
  items: React.ReactNode[];
};

const Slider = ({ items }: SliderProps) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [containerWidth, setContainerWidth] = useState(0);
  const [itemsPerView, setItemsPerView] = useState(1);
  const [index, setIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  const total = items.length;
  const slideWidth = containerWidth / itemsPerView;

  // Calculate if we're at the boundaries
  const isAtStart = index <= 0;
  const isAtEnd = index >= total - itemsPerView;

  useEffect(() => {
    const updateSize = () => {
      if (!containerRef.current) return;
      const width = containerRef.current.offsetWidth;
      setContainerWidth(width);

      if (width >= 1024) setItemsPerView(5);
      else if (width >= 768) setItemsPerView(3);
      else setItemsPerView(1);
    };

    updateSize();
    window.addEventListener("resize", updateSize);
    return () => window.removeEventListener("resize", updateSize);
  }, []);

  const handleNext = () => {
    if (isAnimating || isAtEnd) return;
    setIsAnimating(true);
    setIndex((prev) => prev + 1);
  };

  const handlePrev = () => {
    if (isAnimating || isAtStart) return;
    setIsAnimating(true);
    setIndex((prev) => prev - 1);
  };

  // Reset animation state after transition
  useEffect(() => {
    if (!isAnimating) return;

    const timeout = setTimeout(() => {
      setIsAnimating(false);
    }, 300); // match transition duration

    return () => clearTimeout(timeout);
  }, [isAnimating]);

  return (
    <div ref={containerRef} className="w-full overflow-hidden relative">
      <motion.div
        className="flex"
        animate={{ x: -index * slideWidth }}
        transition={{ type: "tween", duration: 0.3 }}
        style={{ width: `${(total * 100) / itemsPerView}%` }}
      >
        {items.map((item, i) => (
          <div key={i} className="shrink-0 px-2" style={{ width: slideWidth }}>
            {item}
          </div>
        ))}
      </motion.div>
      {/* Controls - only show if not at boundary */}
      {!isAtStart && (
        <Button
          onClick={handlePrev}
          buttonCSS={false}
          classNames="w-10 h-10 rounded-full absolute top-6/9  left-0 -translate-y-1/2   bg-black/40 backdrop-blur shadow cursor-pointer flex items-center justify-center"
        >
          <ChevronLeftIcon width={20} height={20} color="var(--white)" />
        </Button>
      )}

      {!isAtEnd && (
        <Button
          onClick={handleNext}
          buttonCSS={false}
          classNames="w-10 h-10 rounded-full absolute top-6/9 right-0 -translate-y-1/2  bg-black/40 backdrop-blur shadow shadow cursor-pointer flex items-center justify-center"
        >
          <ChevronRightIcon width={20} height={20} color="var(--white)" />
        </Button>
      )}
    </div>
  );
};

export default Slider;
