"use server";

import {
  LandingPageBreederProgramSection,
  LandingPageFeaturedItemsSection,
  LandingPageHeroSection,
  LandingPageProductSection,
  LandingPageResponsibleBreederSection,
} from "@/components";

const LandingPage = async () => {
  return (
    <>
      {/* hero section landing page */}
      <LandingPageHeroSection />
      {/* featured items section landing page */}
      <LandingPageFeaturedItemsSection />
      {/* products section landing page  */}
      <LandingPageProductSection />
      {/* responsible breeder section landing page */}
      <LandingPageResponsibleBreederSection />
      {/* breeder program section landing page */}
      <LandingPageBreederProgramSection />
    </>
  );
};

export default LandingPage;
