"use client";

import { useParams } from "next/navigation";
import {
  AnimalPageImageSection,
  LoadingPAW,
  PageNotFound,
  KennelBioDataSection,
  BreederContactSection,
  BreederKennelSection,
} from "@/components";
import { useGetPublicProfile } from "@/hooks";

const BreederProfileSection = () => {
  const params = useParams();
  const userId = parseInt(params?.breederid as string, 10);
  const { profile, loading, error } = useGetPublicProfile(userId);
  if (loading) {
    return (
      <div className="flex justify-center items-center my-20">
        <LoadingPAW />
      </div>
    );
  }
  if (error || !profile) {
    return <PageNotFound />;
  }

  return (
    <div>
      <AnimalPageImageSection />
      <KennelBioDataSection kennel={profile} />
      <BreederKennelSection  id={profile.user.id}/>
      <BreederContactSection
        phone={profile.phone}
        email={profile.email}
        facebookUrl={profile.facebookUrl}
        instagramUrl={profile.instagramUrl}
      />
    </div>
  );
};

export default BreederProfileSection;
