import { NextImagePlaceHolder } from "@/components";

const AnimalPageImageSection = () => {
  return (
    <>
      <div>
        {/* desktop */}
        <div className="hidden lg:grid w-full grid-cols-1 relative overflow-hidden h-[340] gap-1">
          {[1].map((_, index) => (
            <div key={index} className="flex">
              <NextImagePlaceHolder
                height={800}
                width={800}
                src="/temp.jpg"
                classNames="w-full"
              />
            </div>
          ))}
        </div>
        {/* mobile */}
        <div className="grid lg:hidden w-full relative overflow-hidden h-fit sm:h-[340]">
          {[1].map((_, index) => (
            <div key={index}>
              <NextImagePlaceHolder
                height={800}
                width={800}
                src="/temp.jpg"
                classNames="w-full"
              />
            </div>
          ))}
        </div>
      </div>
    </>
  );
};

export default AnimalPageImageSection;
