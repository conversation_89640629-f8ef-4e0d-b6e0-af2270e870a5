// Textarea.tsx
import React from "react";
import { TextareaProps } from "./_type";

const Textarea: React.FC<TextareaProps> = ({
  label,
  error,
  disabled,
  className = "",
  ...props
}) => {
  return (
    <div className="w-full">
      {label && (
        <label
          htmlFor={props.id || props.name}
          className="block mb-1 text-md font-regular text-(--full-black)"
        >
          {label}
        </label>
      )}
      <textarea
        className={`w-full px-4 py-2 border rounded-lg min-h-[60px] resize-none focus:outline-none focus:ring-1 focus:ring-(--sky-slate-dark)
        focus:border-transparent transition duration-200 ${
          error ? "border-red-500" : "border-(--sky-slate-dark)"
        } ${className} ${disabled ? "opacity-70" : ""}`}
        {...props}
      />
      {error && <p className="mt-1 text-sm text-red-500">{error}</p>}
    </div>
  );
};

export default Textarea;
