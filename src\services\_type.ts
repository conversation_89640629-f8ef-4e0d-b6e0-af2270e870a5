import { IAccessSession, IAuthSession } from "@/tools/cookies";

export interface ICredentials {
  username: string;
  password: string;
}

export type Breed = {
  id: number;
  venom: number;
  breed_id: number;
  name: string;
  species: { id: number; name: string };
  images: {
    id: number;
    image_name: string;
    is_default: boolean;
  };
};

export interface IBaseResponse {
  status: string;
  code: number;
}

export interface IGetBreedsResponse extends IBaseResponse {
  data: {
    results: Breed[];
    count: number;
  };
}

export interface ISignInResponseData extends IAccessSession, IAuthSession {}

export interface ISignInResponse extends IBaseResponse {
  data: ISignInResponseData;
}

export interface IRefreshTokenResponse extends IBaseResponse {
  data: {
    accessToken: string;
  };
}

export type Owner = {
  id: number;
  firstName: string;
  lastName: string;
};

export type Species = {
  id: number;
  name: string;
};

export type Offspring = {
  id?: number;
  registrationName: string;
  registrationNumber: string;
  petName: string;
  gender: string;
  weight?: string;
  weightType?: string;
  colour?: string | null;
  dobDay?: number;
  dobMonth?: number;
  dobYear?: number;
  owner?: Owner;
  species?: Species;
  breed?: Breed;
};

// ✅ Updated Animal type: removed profileId, matches API structure
export type Animal = {
  animal: Offspring;
  about?: string;
  specialTouch?: string;
  createdAt?: string;
  updatedAt?: string;
  dam?: Offspring;
  sire?: Offspring;
};

export type ApiResponse<T> = {
  status: number;
  data: T;
};

export type ApiError = {
  error: string | object;
};

export interface IGetAnimals {
  status: number;
  data: {
    count: number;
    next: string | null;
    previous: string | null;
    results: Animal[];
  };
  message: string;
  errors: unknown;
  code: number;
}

export type AnimalParentSectionProps = {
  dam?: Offspring;
  sire?: Offspring;
};

export interface ParentData {
  registrationName: string;
  petName: string;
  registrationNumber: string;
  species?: { name: string };
  colour?: string | null;
  gender: string;
  dobDay: number;
  dobMonth: number;
  dobYear: number;
  weight: string;
  weightType: string;
}

export interface ParentDataCardProps {
  title: string;
  parent: ParentData;
}

export type AnimalResponseData = {
  animal: Animal;
  about: string;
  specialTouch: string;
  dam: Animal;
  sire: Animal;
  createdAt: string;
  updatedAt: string;
};

export interface RawServerResponse {
  status: string;
  code: number;
  message: string;
  /* eslint-disable-next-line @typescript-eslint/no-explicit-any */
  data: any;
}

export interface KennelOwner {
  ownerId: number;
  firstName: string;
  lastName: string;
}

export interface KennelContact {
  phone: string;
  email: string;
  websiteUrl: string;
  facebookUrl: string;
  instagramUrl: string;
}

export interface KennelAddress {
  addressLine1: string;
  addressLine2: string;
  city: string;
  state: string;
  country: string;
  postalCode: string;
}

export interface KennelAnimal {
  id: number;
  profileId: number;
  name: string;
  type: "parent" | "puppy" | string;
  price: number;
  isAllocated: boolean;
}

export interface KennelData {
  kennelId: number;
  name: string;
  registrationNo: string;
  about: string;
  includeWithPuppies: string;
  behavior: string;
  health: string;
  environment: string;
  specialTouch: string;
  inPersonPickup: boolean;
  domesticAirTravel: boolean;
  internationalAirTravel: boolean;
  groundTransport: boolean;
  placeId: string;
  owner: KennelOwner;
  contact: KennelContact;
  address: KennelAddress;
  animal: KennelAnimal[];
  isVisible: boolean;
  createdAt: string;
}
export interface KennelResponse {
  status: string;
  code: number;
  message: string;
  data: KennelData;
  errors: null | unknown;
}

export interface KennelBioDataSectionProps {
  kennel: KennelData | IUserProfile;
}

export interface AnimalDataViewSectionProps {
  data: KennelAnimal[];
}

export interface AnimalBreedPageAnimalsSectionProps {
  animals: KennelAnimal[];
  title: string;
}

export interface ResultApiResponse {
  status: string;
  code: number;
  message: string;
  data: {
    traits: Trait[];
    diseases: Disease[];
  };
}

export interface Trait {
  id: number;
  product: {
    id: number;
    name: string;
    code: string;
  };
  details: {
    severity: number;
    description: string;
    publication: string;
  };
  interpretation: {
    interpretation: string;
    description: string;
    color: string;
  };
}

export interface Disease {
  id: number;
  product: {
    id: number;
    name: string;
    code: string;
  };
  details: {
    severity: number;
    description: string;
    publication: string;
  };
  interpretation: {
    interpretation: string;
    description: string;
    color: string;
  };
}

export interface IUserProfile {
  profile: {
    about: string;
    createdAt: string;
    email: string | null;
    facebookUrl: string | null;
    id: number;
    instagramUrl: string | null;
    phone: string | null;
    specialTouch: string | null;
    updatedAt: string;
  };
  user: {
    firstName: string;
    id: number;
    lastName: string;
  };
}

export interface IUserProfilePayload {
  about?: string;
  email?: string | null;
  facebook_url?: string | null;
  instagram_url?: string | null;
  phone?: string | null;
  special_touch?: string | null;
  first_name?: string;
  last_name?: string;
}

export interface IGetUserDataResponse extends IBaseResponse {
  data: IUserProfile;
}

export interface IUserKennelListResponse extends IBaseResponse {
  data: { count: number; results: IUserKennel[] };
}

export interface IUserKennel {
  about: string;
  address: {
    addressLine1: string;
    addressLine2: string;
    city: string;
    country: string;
    postalCode: string;
    state: string;
  };
  createdAt: string;
  isVisible: boolean;
  kennelId: number;
  name: string;
  owner: {
    firstName: string;
    lastName: string;
    ownerId: number;
  };
  placeId: string;
  registrationNo: string;
}

export type SaveKennelPayload = {
  name: string;
  registration_no: string;
  about?: string;
  include_with_puppies?: string;
  behavior?: string;
  health?: string;
  environment?: string;
  special_touch?: string;
  in_person_pickup?: boolean;
  domestic_air_travel?: boolean;
  international_air_travel?: boolean;
  ground_transport?: boolean;
  address_line_1?: string;
  address_line_2?: string;
  city?: string;
  state?: string;
  country?: string;
  postal_code?: string;
  place_id?: string;
  phone?: string;
  email?: string;
  website_url?: string;
  facebook_url?: string;
  instagram_url?: string;
};

export interface IUserSaveKennelResponse extends IBaseResponse {
  data: { data: IUserKennel };
}

export interface AnimalResultsData {
  traits: Trait[];
  diseases: Disease[];
}

export interface Trait {
  id: number;
  product: {
    id: number;
    name: string;
    code: string;
  };
  details: {
    severity: number;
    description: string;
    publication: string;
  };
  interpretation: {
    interpretation: string;
    description: string;
    color: string;
  };
}

export interface Disease {
  id: number;
  product: {
    id: number;
    name: string;
    code: string;
  };
  details: {
    severity: number;
    description: string;
    publication: string;
  };
  interpretation: {
    interpretation: string;
    description: string;
    color: string;
  };
}
export interface AnimalResultViewSectionProps {
  animalId: number;
}

export interface ApiResponseWithData {
  code: number;
  data: unknown;
}
export interface ApiSuccessResponse<T> {
  code: 200;
  data: T;
}
export interface IUserProfile {
  profileId: number;
  about: string;
  specialTouch: string;
  phone: string;
  email: string;
  facebookUrl: string;
  instagramUrl: string;
  user: {
    id: number;
    firstName: string;
    lastName: string;
  };
  createdAt: string;
  uodatedAt: string;
}

export interface IUserProfileResponse {
  status: string;
  code: number;
  message: string;
  data: IUserProfile;
  errors: unknown;
}

export interface ExtendedKennelBioDataSectionProps
  extends KennelBioDataSectionProps {
  iskennel: boolean;
}

export interface BreederContactProps {
  phone: string;
  email: string;
  facebookUrl?: string;
  instagramUrl?: string;
}

export interface IKennelUpdatePayload {
  visible?: boolean;
  archive?: boolean;
  active?: boolean;
}
