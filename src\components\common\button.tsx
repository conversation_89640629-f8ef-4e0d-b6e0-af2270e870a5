import clsx from "clsx";
import { ButtonProps } from "./_type";
import LoadingSpinner from "./loading-spinner";

const initCSS =
  "h-10 rounded-3xl min-w-32 flex justify-center items-center px-6 w-fit cursor-pointer\t";

const Button = ({
  type,
  title,
  classNames,
  outLine,
  children,
  disable,
  contentEND,
  onClick,
  buttonCSS = true,
  loading,
}: ButtonProps) => {
  let buttonStyle = "";

  if (buttonCSS) {
    buttonStyle += initCSS;
  }
  switch (type) {
    case "Primary":
      buttonStyle += outLine
        ? "px-2! border border-(--violet) text-(--violet)\t "
        : "bg-(--violet) text-white\t";
      break;
    case "Secondary":
      buttonStyle += outLine
        ? "px-2! border border-(--violet-medium) text-(--violet-medium)\t "
        : "bg-(--violet-medium) text-white\t";
      break;
    case "type2Primary":
      buttonStyle += outLine
        ? "px-2! border border-(--orange) text-(--orange)\t "
        : "bg-(--orange) text-white\t";
      break;
    default:
      buttonStyle += outLine
        ? "px-2! border border-(--violet) text-(--violet)\t "
        : "bg-(--violet) text-white\t";
  }

  return (
    <button
      className={clsx(
        classNames,
        buttonStyle,
        disable || loading ? "opacity-45" : ""
      )}
      onClick={onClick}
    >
      <>
        {loading ? (
          <LoadingSpinner />
        ) : (
          <>
            {contentEND ? (
              <>
                {children}
                {title}
              </>
            ) : (
              <>
                {title}
                {children}
              </>
            )}
          </>
        )}
      </>
    </button>
  );
};

export default Button;
