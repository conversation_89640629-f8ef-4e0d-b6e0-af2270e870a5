"use server";

import server<PERSON>pi from "@/lib/server-api";
import { IGetUserDataResponse, IUserProfilePayload } from "../_type";
import { ApiError, ApiResponse } from "@/lib/_lib.type";

const getUserData = async (
  method: "GET" | "PUT",
  payload?: IUserProfilePayload
): Promise<ApiResponse<IGetUserDataResponse | ApiError>> => {
  return await serverApi({
    url: "paw-print/profile/me/",
    method: method,
    isProtected: true,
    body: payload ? JSON.stringify(payload) : undefined,
  });
};

export default getUserData;
