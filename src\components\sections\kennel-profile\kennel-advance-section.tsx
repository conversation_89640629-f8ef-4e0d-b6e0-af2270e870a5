"use client";

import { ContentContainer, SectionContainer } from "@/components";
import {
  HOW_DO_WE_CARE,
  HOW_DO_WE_TRANSPORT,
  InfoSections,
  TransportSections,
} from "@/constants/text-objects";
import { KennelBioDataSectionProps } from "@/services/_type";

const KennelAdvanceSection: React.FC<KennelBioDataSectionProps> = ({
  kennel,
}) => {
  if (!kennel) return null;

  // Helper to safely render kennel field values
  const renderValue = (value: unknown) => {
    if (typeof value === "string" || typeof value === "number") return value;
    if (typeof value === "boolean") return value ? "Yes" : "No";
    if (value && typeof value === "object")
      return JSON.stringify(value, null, 2);
    return "No additional details available.";
  };

  return (
    <SectionContainer>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ContentContainer classNames="rounded-2xl w-full">
          <div className="text-2xl font-semibold">{HOW_DO_WE_CARE}</div>
          {InfoSections.map(({ Icon, title, field }) => {
            const value = kennel[field as keyof typeof kennel];
            return (
              <div
                key={field}
                className="grid grid-cols-[auto_1fr] gap-x-4 gap-y-5 items-start my-8"
              >
                <Icon />
                <div>
                  <div className="text-xl font-medium">{title}</div>
                  <div className="text-md text-gray-600 lg:w-[500px]">
                    {renderValue(value)}
                  </div>
                </div>
              </div>
            );
          })}
        </ContentContainer>
        <ContentContainer classNames="rounded-2xl w-full relative">
          <div className="text-2xl font-semibold">{HOW_DO_WE_TRANSPORT}</div>
          {TransportSections.map(({ Icon, title, content, field }) =>
            kennel[field as keyof typeof kennel] ? (
              <div
                key={field}
                className={`grid grid-cols-[auto_1fr] gap-x-4 gap-y-5 items-start ${
                  field !== "groundTransport" ? "my-8" : ""
                }`}
              >
                <Icon />
                <div>
                  <div className="text-xl font-medium">{title}</div>
                  <div className="text-md text-gray-600 lg:w-[500px]">
                    {content}
                  </div>
                </div>
              </div>
            ) : null
          )}
        </ContentContainer>
      </div>
    </SectionContainer>
  );
};

export default KennelAdvanceSection;
