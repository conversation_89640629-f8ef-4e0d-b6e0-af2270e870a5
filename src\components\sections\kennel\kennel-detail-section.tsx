"use client";

import React, { useCallback } from "react";
import { Button, Textarea, MultiSelect } from "@/components";
import { kennelMoredata } from "@/constants/input-objects";
import {
  KENNEL_TRANSPORT_OPTIONS,
  UPDATE_KENNEL,
  KENNEL_ADVANCED_DETAILS_TITLE,
  KENNEL_TRANSPORT_TITLE,
} from "@/constants/text-objects";
import { userKennelStore } from "@/tools/state-lib/kennel.store";
import { SaveKennelPayload } from "@/services";
import { useSaveUserKennel } from "@/hooks";

const KennelDetailSection = () => {
  const { saveNewKennel, newKennel } = userKennelStore();
  const { callSaveUserKennel, savingKennel } = useSaveUserKennel();

  const handleAddKennel = useCallback(async () => {
    if (newKennel) {
      callSaveUserKennel(newKennel);
    }
  }, [callSaveUserKennel, newKennel]);

  const handleSelectChange = (selected: { label: string; value: string }[]) => {
    KENNEL_TRANSPORT_OPTIONS.forEach((option) =>
      saveNewKennel({
        [option.value]: selected.includes(option),
      } as unknown as SaveKennelPayload)
    );
  };

  return (
    <div className="flex flex-col">
      <div className="text-lg text-(--full-black)">
        {KENNEL_ADVANCED_DETAILS_TITLE}
      </div>
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 mt-4">
        {kennelMoredata.map(({ id, label, placeholder }) => (
          <div className="mt-2" key={id}>
            <Textarea
              id={id}
              name={id}
              label={label}
              placeholder={placeholder}
              onChange={({ target: { value } }) =>
                saveNewKennel({ [id]: value } as unknown as SaveKennelPayload)
              }
              defaultValue={
                newKennel
                  ? (newKennel[id as keyof SaveKennelPayload] as string)
                  : ""
              }
            />
          </div>
        ))}
      </div>
      <div className="mt-4">
        <MultiSelect
          title={KENNEL_TRANSPORT_TITLE}
          options={KENNEL_TRANSPORT_OPTIONS}
          onChange={handleSelectChange}
          defaultSelected={KENNEL_TRANSPORT_OPTIONS.filter(
            (option) =>
              !!(
                newKennel &&
                !!newKennel[option.value as keyof SaveKennelPayload]
              )
          )}
        />
      </div>

      <Button
        title={UPDATE_KENNEL}
        type="type2Primary"
        classNames="mt-6 ml-auto"
        onClick={handleAddKennel}
        loading={savingKennel}
      />
    </div>
  );
};

export default KennelDetailSection;
