import Image from "next/image";
import { NextImagePlaceHolderProps } from "./_type";

const NextImagePlaceHolder = ({
  LCP,
  height,
  width,
  lazyLoad,
  src,
  classNames,
  alt,
}: NextImagePlaceHolderProps) => {
  return (
    <Image
      className={classNames}
      width={width}
      height={height}
      src={src}
      loading={lazyLoad ? "lazy" : undefined}
      fetchPriority={LCP ? "high" : undefined}
      priority={!!LCP}
      alt={alt ?? "image"}
    />
  );
};

export default NextImagePlaceHolder;
