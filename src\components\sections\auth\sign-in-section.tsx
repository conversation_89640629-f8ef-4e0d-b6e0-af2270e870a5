"use client";

import clsx from "clsx";
import {
  <PERSON><PERSON>,
  ContentContainer,
  DynamicColorPolygon,
  Heading,
  Input,
  NextImagePlaceHolder,
  SectionContainer,
} from "@/components";
import { polygonConfigs } from "@/constants/raw-objects";
import {
  useAuthNavigate,
  useSignIn,
  useClearData,
  useGetUserData,
} from "@/hooks";
import { useCallback, useState } from "react";
import { USER_PROFILE_URL_STATIC } from "@/constants/route-objects";

const AuthPageSignInSection = () => {
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const { callCredentialsSignIn } = useSignIn();
  const { callProtectedRouteNavigate } = useAuthNavigate();
  const { callGetUserData } = useGetUserData();
  useClearData(true);

  const handleSignInOnClick = useCallback(async () => {
    setLoading(true);
    await callCredentialsSignIn({ username, password });
    await callGetUserData();
    await callProtectedRouteNavigate((userID) => {
      if (typeof window !== "undefined") {
        localStorage.setItem("status", "login");
      }
      return `${USER_PROFILE_URL_STATIC}/${userID}`;
    });
    setLoading(false);
  }, [
    callCredentialsSignIn,
    callGetUserData,
    callProtectedRouteNavigate,
    password,
    username,
  ]);

  return (
    <div className="flex h-full bg-(--gray) relative">
      <SectionContainer
        classNames="flex h-full relative justify-end"
        background="SkySlate"
      >
        {/* curves */}
        <div className="hidden md:inline-block h-full w-4/6 md:w-2/3 absolute z-10 left-0 top-0 bg-(--gray) rounded-tr-full border-2 border-(--violet-light)" />
        {/*end of curves */}
        <ContentContainer classNames="flex justify-center items-center md:inline-block">
          <NextImagePlaceHolder
            height={300}
            width={300}
            classNames="h-[220px] md:h-[300px] w-auto absolute -bottom-[76px] md:-bottom-[105px] opacity-100 sm:inline-block z-[10] md:right-0"
            src="/signin-page/dog.png"
          />
          {/* p-6 overflow-hidden md:-ml-6 */}
          <div className="flex w-full sm:max-w-[560px] h-full z-20 relative items-center">
            <div className="w-full h-full absolute overflow-hidden">
              {polygonConfigs.slice(0, -1).map((polygon, idx) => (
                <DynamicColorPolygon
                  key={idx}
                  classNames={clsx(polygon.classNames, "-z-10 blur-none!")}
                />
              ))}
            </div>
            <div className="h-fit w-full flex gap-4 flex-col justify-center mb-[126px] md:mb-0 glass-ef p-8">
              <Heading
                title="Sign in to Orivet Paw Print Pedigree"
                type="Hx1"
                classNames="py-6"
                lowContrast
              />
              <Input
                placeholder="Username"
                onChange={({ target: { value } }) => setUsername(value)}
              />
              <Input
                placeholder="Password"
                onChange={({ target: { value } }) => setPassword(value)}
              />
              <div className="flex items-center justify-center">
                <Button
                  title="Sign in"
                  type="type2Primary"
                  classNames="w-full sm:max-w-[200px]"
                  onClick={handleSignInOnClick}
                  loading={loading}
                />
              </div>
            </div>
          </div>
        </ContentContainer>
      </SectionContainer>
    </div>
  );
};

export default AuthPageSignInSection;
