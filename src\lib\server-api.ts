"use server";

import {
  getAccessC<PERSON>ie,
  getAuth<PERSON><PERSON>ie,
  getRefresh<PERSON><PERSON>ie,
  setAccessTokenCookie,
  clearCookies,
} from "@/tools/cookies";
import { getServerConfig } from "../tools/config/protected-config";
import { ApiError, ApiResponse, ServerApiProps } from "./_lib.type";
import { debugLogs } from "./mode.debug";
import { convertKeysToCamelCase } from "./string-convert";
import { getNewTokens } from "@/services";
import { IRefreshTokenResponse } from "@/services/_type";

const serverApi = async <T>(
  props: ServerApiProps
): Promise<ApiResponse<T | ApiError>> => {
  const {
    url,
    body,
    isProtected,
    headers = {},
    customFetchPolicy,
    method = "POST",
  } = props;
  const config = await getServerConfig();
  let apiHeaders: Record<string, string> = {
    ...headers,
    "x-api-key": config.X_API_KEY,
    "Content-Type": "application/json",
  };

  const refreshToken = await getRefreshCookie();
  const accessToken = await getAccessCookie();
  if (isProtected && (refreshToken || accessToken)) {
    const authData = await getAuthCookie();
    const username = authData?.user.username;

    if (accessToken) {
      apiHeaders = { ...apiHeaders, Authorization: `Bearer ${accessToken}` };
    } else if (refreshToken && username?.length) {
      try {
        debugLogs("token update..........");

        const tokens = await getNewTokens(username, refreshToken);
        if (tokens.status === 200) {
          debugLogs("token update success..........");
          const newAccessToken = (tokens.data as IRefreshTokenResponse).data
            .accessToken;
          // new token save
          await setAccessTokenCookie(newAccessToken);
          apiHeaders = {
            ...apiHeaders,
            Authorization: `Bearer ${newAccessToken}`,
          };
        }
      } catch (err) {
        debugLogs(err);
        await clearCookies();
      }
    } else {
      await clearCookies();
    }
  }

  try {
    const response = await fetch(config.BASE_URL.concat(url), {
      method,
      body: body ?? undefined,
      headers: apiHeaders,
      cache: customFetchPolicy,
    });
    if (!response.ok) {
      const error = await response.json();
      const noAuth =
        error?.data?.detail === "Authentication credentials were not provided.";
      if (noAuth) {
        await clearCookies();
      }
      await debugLogs({
        URL: config.BASE_URL.concat(url),
        error: typeof error?.data === "object" ? error?.data[0] : error,
        errorTopLevel: error?.data,
      });
      return {
        status: error?.code,
        data: {
          error: typeof error?.data === "object" ? error?.data[0] : error,
        },
      };
    }
    const data = convertKeysToCamelCase(await response.json());
    await debugLogs({ URL: config.BASE_URL.concat(url), data });

    return { data, status: data?.code };
  } catch (error: unknown) {
    await debugLogs({ URL: config.BASE_URL.concat(url), error });
    return {
      status: 400,
      data: { error: error as string },
    };
  }
};

export default serverApi;
