"use client";

import React, { useEffect, useMemo, useState } from "react";
import { motion } from "framer-motion";
import clsx from "clsx";
import { ColorPolygonProps } from "./_type";

const DynamicColorPolygon = ({ classNames }: ColorPolygonProps) => {
  const colors = useMemo(() => ["--sky-blue", "--sky-orange"], []);
  const [currentColor, setCurrentColor] = useState(colors[0]);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentColor((prevColor) =>
        prevColor === colors[0] ? colors[1] : colors[0]
      );
    }, 3000);

    return () => clearInterval(interval);
  }, [colors]);

  return (
    <motion.div
      className={clsx(classNames, "bg-(--sky-blue) h-12 w-12")}
      style={{ backgroundColor: `var(${currentColor})` }}
      animate={{
        backgroundColor: `var(${currentColor})`,
      }}
      transition={{
        duration: 3,
        ease: "easeInOut",
      }}
    />
  );
};

export default DynamicColorPolygon;
