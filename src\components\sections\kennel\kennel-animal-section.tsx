"use client";

import React, { useState } from "react";
import { Avatar, Button, ImageButtonCard, PopupModal } from "@/components";
import { PencilIcon, TrashIcon } from "@heroicons/react/24/outline";
import { useRouter } from "next/navigation";
import clsx from "clsx";
import {
  KENNEL_PROFILE_TITLE,
  ADD_PET,
  PUPPIES,
  PARENTS,
  SELECT_PET,
} from "@/constants/text-objects";

// @todo: Replace with real pet data from API
const dogData = [
  {
    id: 1,
    breed: "French Bulldog",
    type: "puppy",
    description: "Small, affectionate companion",
  },
  {
    id: 2,
    breed: "Golden Retriever",
    type: "puppy",
    description: "Friendly and intelligent",
  },
  {
    id: 3,
    breed: "German Shepherd",
    type: "puppy",
    description: "Strong and obedient",
  },
  { id: 4, breed: "Bulldog", type: "parent", description: "Gentle and calm" },
  { id: 5, breed: "Poodle", type: "puppy", description: "Smart and stylish" },
  { id: 6, breed: "test", type: "parent", description: "Smart and stylish" },
];
// @todo: Replace with real pet data from API
const mockPopupPets = Array.from({ length: 22 }, (_, i) => ({
  id: i + 100,
  name: `Pet ${i + 1}`,
  type: i % 2 === 0 ? "puppy" : "parent",
  avatarUrl: "/temp.jpg",
}));

const KennelAnimalSection: React.FC = () => {
  const router = useRouter();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedPetId, setSelectedPetId] = useState<number | null>(null);
  const handleClick = () => {
    router.push("/user-profile/11/animal/12");
  };
  const puppyDogs = dogData.filter((dog) => dog.type === "puppy");
  const parentDogs = dogData.filter((dog) => dog.type === "parent");
  const handleSelectPet = (id: number) => {
    setSelectedPetId(id === selectedPetId ? null : id);
  };
  const handleAddSelected = () => {
    const selectedPet = mockPopupPets.find((pet) => pet.id === selectedPetId);
    console.log("Add pet:", selectedPet);
    setIsModalOpen(false);
  };

  return (
    <div className="flex flex-col p-4 border border-(--sky-slate-dark2) rounded-lg mt-4">
      {/* Header */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
        <div className="text-lg font-semibold">{KENNEL_PROFILE_TITLE}</div>
        <div className="flex justify-end">
          <Button
            title={ADD_PET}
            type="Secondary"
            onClick={() => setIsModalOpen(true)}
          />
        </div>
      </div>
      {/* Dogs Display */}
      <div className="w-full space-y-8 mt-6">
        {/* Puppies */}
        <div>
          <h2 className="text-xl font-semibold mb-4 text-full-black">
            {PUPPIES}
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 lg:grid-cols-4 xl:grid-cols-6 gap-5">
            {puppyDogs.map((dog) => (
              <div key={dog.id}>
                <ImageButtonCard
                  imgSrc="/temp.jpg"
                  alt={dog.breed}
                  onFirstButtonClick={() => console.log(`Edit ${dog.breed}`)}
                  onSecondButtonClick={() => console.log(`Delete ${dog.breed}`)}
                  firstButtonIcon={
                    <PencilIcon
                      className="h-6 w-6 text-blue-500"
                      onClick={handleClick}
                    />
                  }
                  secondButtonIcon={
                    <TrashIcon className="h-6 w-6 text-red-500" />
                  }
                />
                <div className="text-lg font-semibold ">{dog.breed}</div>
                <div className="text-md">{dog.description}</div>
              </div>
            ))}
          </div>
        </div>
        {/* Parents */}
        <div>
          <h2 className="text-xl font-semibold mb-4 text-full-black">
            {PARENTS}
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 lg:grid-cols-4 xl:grid-cols-6 gap-5">
            {parentDogs.map((dog) => (
              <div key={dog.id}>
                <ImageButtonCard
                  imgSrc="/temp.jpg"
                  alt={dog.breed}
                  onFirstButtonClick={() => console.log(`Edit ${dog.breed}`)}
                  onSecondButtonClick={() => console.log(`Delete ${dog.breed}`)}
                  firstButtonIcon={
                    <PencilIcon className="h-6 w-6 text-blue-500" />
                  }
                  secondButtonIcon={
                    <TrashIcon className="h-6 w-6 text-red-500" />
                  }
                />
                <div className="text-lg font-semibold mt-2">{dog.breed}</div>
                <div className="text-md">{dog.description}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
      {/* PopUp */}
      <PopupModal
        title={SELECT_PET}
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        footer={
          <div className=" flex justify-end">
            <Button title="Add" type="Primary" onClick={handleAddSelected} />
          </div>
        }
      >
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {mockPopupPets.map((pet) => (
            <div
              key={pet.id}
              onClick={() => handleSelectPet(pet.id)}
              className={clsx(
                "border rounded-lg p-4 flex items-center gap-4 cursor-pointer transition",
                selectedPetId === pet.id
                  ? "border-purple-500 bg-purple-50"
                  : "border-(--sky-slate-dark2) hover:bg-gray-100"
              )}
            >
              <Avatar src={pet.avatarUrl} alt={pet.name} size={20} />
              <div>
                <div className="font-semibold text-full-black">{pet.name}</div>
                <div className="text-sm text-gray-600 capitalize">
                  {pet.type}
                </div>
              </div>
            </div>
          ))}
        </div>
      </PopupModal>
    </div>
  );
};

export default KennelAnimalSection;
