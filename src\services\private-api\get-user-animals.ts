"use server";

import server<PERSON>pi from "@/lib/server-api";
import { IGetUserDataResponse } from "../_type";
import { ApiError, ApiResponse } from "@/lib/_lib.type";

const getUserAnimals = async (): Promise<
  ApiResponse<IGetUserDataResponse | ApiError>
> => {
  return await serverApi({
    url: "paw-print/my/animals/",
    method: "GET",
    isProtected: true,
  });
};

export default getUserAnimals;
