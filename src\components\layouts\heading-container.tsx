import Heading from "../common/heading";
import NextImagePlaceHolder from "../common/next-image-placeholder";
import RingPulse from "../common/ring-pulse";
import ContentContainer from "./content-container";
import SectionContainer from "./section-container";
import { HeadingContainerProps } from "./_type";

const HeadingContainer = ({
  background = "SkySlate",
  src,
  heading,
  description,
}: HeadingContainerProps) => {
  return (
    <SectionContainer background={background}>
      <ContentContainer classNames="rounded-2xl h-fit w-full relative flex flex-col">
        {/* heading */}
        <div className="h-full items-center flex gap-8 flex-col md:flex-row">
          {!!src && (
            <div className="w-[200] h-[200] relative flex items-center justify-center">
              <div className="h-[200] w-[200] rounded-full overflow-hidden">
                <NextImagePlaceHolder
                  src={src}
                  width={200}
                  height={200}
                  classNames="relative z-10 scale-120"
                />
                <RingPulse duration={1.4} size={[1, 1.5]} />
              </div>
            </div>
          )}
          <div className="h-full flex flex-col justify-center items-start gap-8">
            {heading && <Heading title={heading} type="Title" />}
            {description && <Heading title={description} />}
          </div>
        </div>
        {/* content */}
      </ContentContainer>
    </SectionContainer>
  );
};

export default HeadingContainer;
