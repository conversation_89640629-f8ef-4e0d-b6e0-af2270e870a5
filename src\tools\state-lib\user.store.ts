import { IUserProfile } from "@/services";
import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";
import { USER_STORE } from "@/constants/store-object";
import { UserDataStore } from "./_type";
// import ClubEncryption from "../encryption";

export const userDataStore = create<UserDataStore>()(
  persist(
    (set) => ({
      userData: null,
      saveUserData: (userData: IUserProfile | null) =>
        set(() => ({ userData })),
      clearUserData: () => () => set({ userData: null }),
    }),
    {
      name: USER_STORE,
      storage: createJSONStorage(() => {
        // const clubEncryption = ClubEncryption.getInstance();
        return {
          getItem: (name) => {
            return localStorage.getItem(name) ?? "";
          },
          setItem: (name, value) => {
            localStorage.setItem(name, value);
          },
          removeItem: (name) => {
            localStorage.removeItem(name);
          },
        };
      }),
    }
  )
);
