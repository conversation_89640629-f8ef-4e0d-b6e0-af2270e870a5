"use client";

import { useParams } from "next/navigation";
import {
  HeadingContainer,
  AnimalDataSection,
  AnimalDescriptionSection,
  LoadingPAW,
  AnimalParentSection,
  AnimalResultViewSection,
  AnimalCheckoutSection,
  PageNotFound,
} from "@/components";
import { useGetSingleAnimal } from "@/hooks";

const AnimalProfileSection = () => {
  const params = useParams();
  const animalId = parseInt(params?.animalid as string, 10);
  const { animal, loading, error } = useGetSingleAnimal(animalId);
  if (loading) {
    return (
      <div className="flex justify-center items-center my-20">
        <LoadingPAW />
      </div>
    );
  }
  if (error || !animal) {
    return <PageNotFound />;
  }

  return (
    <div>
      <HeadingContainer
        heading={animal.animal?.petName}
        description={animal.about}
        src="/temp2.png"
      />
      <AnimalDescriptionSection description={animal.specialTouch} />
      <AnimalDataSection {...animal.animal} />
      <AnimalResultViewSection animalId={animalId} />
      <AnimalParentSection dam={animal.dam} sire={animal.sire} />
      <AnimalCheckoutSection />
    </div>
  );
};

export default AnimalProfileSection;
