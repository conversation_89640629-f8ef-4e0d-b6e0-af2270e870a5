"use client";

import {
  ContentContainer,
  SectionContainer,
  ParentDataCard,
} from "@/components";
import { AnimalParentSectionProps } from "@/services/_type";


const AnimalParentSection = ({ dam, sire }: AnimalParentSectionProps) => {
  if (!dam && !sire) return null;
  return (
    <SectionContainer background="Gray">
      <ContentContainer classNames="rounded-2xl  w-full relative flex flex-col gap-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 w-full">
          {dam && (
            <ParentDataCard
              title="Dam"
              parent={{
                registrationName: dam.registrationName,
                petName: dam.petName,
                registrationNumber: dam.registrationNumber,
                species: dam.species,
                colour: dam.colour ?? "N/A",
                gender: dam.gender,
                dobDay: dam.dobDay ?? 0,
                dobMonth: dam.dobMonth ?? 0,
                dobYear: dam.dobYear ?? 0,
                weight: dam.weight ?? "0.00",
                weightType: dam.weightType ?? "",
              }}
            />
          )}
          {sire && (
            <ParentDataCard
              title="Sire"
              parent={{
                registrationName: sire.registrationName,
                petName: sire.petName,
                registrationNumber: sire.registrationNumber,
                species: sire.species,
                colour: sire.colour ?? "N/A",
                gender: sire.gender,
                dobDay: sire.dobDay ?? 0,
                dobMonth: sire.dobMonth ?? 0,
                dobYear: sire.dobYear ?? 0,
                weight: sire.weight ?? "0.00",
                weightType: sire.weightType ?? "",
              }}
            />
          )}
        </div>
      </ContentContainer>
    </SectionContainer>
  );
};

export default AnimalParentSection;
