"use server";

import server<PERSON>pi from "@/lib/server-api";
import { IKennelUpdatePayload, IUserSaveKennelResponse } from "../_type";
import { ApiError, ApiResponse } from "@/lib/_lib.type";

const updateUserKennelStatus = async (
  id: string,
  payload: IKennelUpdatePayload
): Promise<ApiResponse<IUserSaveKennelResponse | ApiError>> => {
  return await serverApi({
    url: `paw-print/my/kennels/${id}`,
    method: "PATCH",
    isProtected: true,
    body: JSON.stringify(payload),
  });
};

export default updateUserKennelStatus;
