"use client";

import {
  Content<PERSON>ontaine<PERSON>,
  <PERSON><PERSON>,
  ImageCard,
  Section<PERSON><PERSON>r,
  <PERSON>lider,
} from "@/components";
import { useGetBreeds } from "@/hooks";

const FeaturedItemsSection = () => {
  const { breeds } = useGetBreeds(true);
  console.log(breeds)
  return (
    <SectionContainer
      background="Gray"
      classNames="min-h-[500px] h-fit w-full flex justify-center items-center relative"
    >
      <div className="bg-(--gray) w-full h-[250px] top-0 absolute skin">
        <div className="bg-(--sky-slate) w-full h-[250px] top-0 absolute rounded-br-full skin" />
      </div>
      <div className="bg-(--sky-slate) w-full h-[250px] top-[250px] absolute skin" />
      <div className="bg-(--gray) w-full h-[250px] top-[250px] absolute rounded-tl-full border border-b-0 border-t-0 border-(--violet-light) skin" />
      <ContentContainer classNames="flex">
        <div className="flex flex-col gap-6 w-full z-10 -mt-6">
          <Heading title="Great for families" type="Hx1" />
          <Slider
            items={breeds.map(({ name }, index) => (
              <ImageCard key={index} title={name} />
            ))}
          />
        </div>
      </ContentContainer>
    </SectionContainer>
  );
};

export default FeaturedItemsSection;
