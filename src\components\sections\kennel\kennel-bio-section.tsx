"use client";

import React, { useEffect, useState } from "react";
import { Input, Button, Textarea } from "@/components";
import {
  kennelInfo,
  kennelAboutField,
  kennelSocialAndContact,
  kennelAddressDetails,
} from "@/constants/input-objects";
import {
  KENNEL_DETAILS_TITLE,
  SOCIAL_MEDIA_CONTACT_DETAILS,
  ADDRESS_DETAILS,
  VIEW_KENNEL,
} from "@/constants/text-objects";
import { SaveKennelPayload } from "@/services";
import { userKennelStore } from "@/tools/state-lib/kennel.store";

const KennelBioSection = () => {
  const [kennelData, setKennelData] = useState<SaveKennelPayload | null>(null);
  const { saveNewKennel, newKennel } = userKennelStore();

  // save kennel main data
  useEffect(() => {
    if (kennelData) {
      saveNewKennel(kennelData);
    }
  }, [kennelData, saveNewKennel]);

  return (
    <div className="flex flex-col">
      <div className="grid grid-cols-2 items-center gap-4">
        <div className="text-lg text-(--full-black)">
          {KENNEL_DETAILS_TITLE}
        </div>
        <div className="flex justify-end">
          <Button
            title={VIEW_KENNEL}
            type="Primary"
            onClick={() => window.open("/profile/11", "_blank")}
          />
        </div>
      </div>
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 mt-4">
        {kennelInfo.map(({ id, label, placeholder }) => (
          <Input
            key={id}
            id={id}
            name={id}
            label={label}
            placeholder={placeholder}
            onChange={({ target: { value } }) => {
              setKennelData(
                (prv) => ({ ...prv, [id]: value } as SaveKennelPayload)
              );
            }}
            defaultValue={
              newKennel
                ? (newKennel[id as keyof SaveKennelPayload] as string)
                : ""
            }
          />
        ))}
      </div>
      {kennelAboutField.map(({ id, label, placeholder }) => (
        <div className="mt-2" key={id}>
          <Textarea
            id={id}
            name={id}
            label={label}
            placeholder={placeholder}
            onChange={({ target: { value } }) => {
              setKennelData(
                (prv) => ({ ...prv, [id]: value } as SaveKennelPayload)
              );
            }}
            defaultValue={
              newKennel
                ? (newKennel[id as keyof SaveKennelPayload] as string)
                : ""
            }
          />
        </div>
      ))}
      <div className="text-lg text-(--full-black) mt-4 mb-3">
        {SOCIAL_MEDIA_CONTACT_DETAILS}
      </div>
      {kennelSocialAndContact
        .filter((input) => input.cols === 1)
        .map(({ id, label, placeholder }) => (
          <Input
            key={id}
            id={id}
            name={id}
            label={label}
            placeholder={placeholder}
            className="mt-2"
            onChange={({ target: { value } }) => {
              setKennelData(
                (prv) => ({ ...prv, [id]: value } as SaveKennelPayload)
              );
            }}
            defaultValue={
              newKennel
                ? (newKennel[id as keyof SaveKennelPayload] as string)
                : ""
            }
          />
        ))}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 mt-4">
        {kennelSocialAndContact
          .filter((input) => input.cols === 2)
          .map(({ id, label, placeholder }) => (
            <Input
              key={id}
              id={id}
              name={id}
              label={label}
              placeholder={placeholder}
              onChange={({ target: { value } }) => {
                setKennelData(
                  (prv) => ({ ...prv, [id]: value } as SaveKennelPayload)
                );
              }}
              defaultValue={
                newKennel
                  ? (newKennel[id as keyof SaveKennelPayload] as string)
                  : ""
              }
            />
          ))}
      </div>
      <div className="text-lg text-(--full-black) mt-4 mb-3">
        {ADDRESS_DETAILS}
      </div>
      <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
        {kennelAddressDetails.map(({ id, label, placeholder }) => (
          <Input
            key={id}
            id={id}
            name={id}
            label={label}
            placeholder={placeholder}
            onChange={({ target: { value } }) => {
              setKennelData(
                (prv) => ({ ...prv, [id]: value } as SaveKennelPayload)
              );
            }}
            defaultValue={
              newKennel
                ? (newKennel[id as keyof SaveKennelPayload] as string)
                : ""
            }
          />
        ))}
      </div>
    </div>
  );
};

export default KennelBioSection;
