// store types

import { IUserProfile, SaveKennelPayload } from "@/services";

export type BearStore = {
  bears: number;
  addABear: (data: number) => void;
};

export type KennelStore = {
  newKennel: SaveKennelPayload | null;
  saveNewKennel: (data: SaveKennelPayload | null) => void;
  clearSavedKennel: () => void;
};

export type UserDataStore = {
  userData: IUserProfile | null;
  saveUserData: (data: IUserProfile | null) => void;
  clearUserData: () => void;
};
