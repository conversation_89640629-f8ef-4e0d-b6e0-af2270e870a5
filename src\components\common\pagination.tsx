"use client";

import type React from "react";
import clsx from "clsx";
import Button from "./button";
import {
  ChevronDoubleLeftIcon,
  ChevronDoubleRightIcon,
} from "@heroicons/react/24/outline";

export function Pagination({
  "aria-label": ariaLabel = "Page navigation",
  className,
  ...props
}: React.ComponentPropsWithoutRef<"nav">) {
  return (
    <nav
      aria-label={ariaLabel}
      {...props}
      className={clsx(className, "flex gap-x-2")}
    />
  );
}

export function PaginationPrevious({
  className,
  onClick,
  children = "Previous",
  disable,
}: React.PropsWithChildren<{
  href?: string | null;
  className?: string;
  onClick?: () => void;
  disable?: boolean;
}>) {
  return (
    <span className={clsx(className, "grow basis-0")} onClick={onClick}>
      <Button outLine disable={disable}>
        <ChevronDoubleLeftIcon width={24} height={24} color={"var(--violet)"} />
        {children}
      </Button>
    </span>
  );
}

export function PaginationNext({
  className,
  onClick,
  children = "Next",
  disable,
}: React.PropsWithChildren<{
  href?: string | null;
  className?: string;
  onClick?: () => void;
  disable?: boolean;
}>) {
  return (
    <span
      className={clsx(className, "flex grow basis-0 justify-end")}
      onClick={onClick}
    >
      <Button outLine disable={disable}>
        {children}
        <ChevronDoubleRightIcon
          width={24}
          height={24}
          color={"var(--violet)"}
        />
      </Button>
    </span>
  );
}

export function PaginationList({
  className,
  ...props
}: React.ComponentPropsWithoutRef<"span">) {
  return (
    <span
      {...props}
      className={clsx(className, "hidden items-baseline gap-x-2 sm:flex")}
    />
  );
}

export function PaginationPage({
  current = false,
  children,
  onClick,
}: React.PropsWithChildren<{
  href: string;
  className?: string;
  current?: boolean;
  onClick?: () => void;
}>) {
  return (
    <Button
      type="Primary"
      outLine={!current}
      aria-current={current ? "page" : undefined}
      classNames={"min-w-14!"}
      onClick={onClick}
    >
      <span className="-mx-0.5">{children}</span>
    </Button>
  );
}

export function PaginationGap({
  className,
  children = <>&hellip;</>,
  ...props
}: React.ComponentPropsWithoutRef<"span">) {
  return (
    <span
      aria-hidden="true"
      {...props}
      className={clsx(
        className,
        "w-[2.25rem] text-center text-sm/6 font-semibold select-none text-(--violet)"
      )}
    >
      {children}
    </span>
  );
}
