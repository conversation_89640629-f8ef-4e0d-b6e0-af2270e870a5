"use client";

import { useEffect, useState } from "react";
import { IUserProfile } from "@/services";
import { IUserProfileResponse, ApiSuccessResponse } from "@/services/_type";
import getProfileById from "@/services/public-api/get-profile";
import { debugLogs } from "@/lib/mode.debug";
import { isApiError } from "@/lib/api-guard";

// Type guard: Check if object has numeric 'code'
const hasCode = (obj: unknown): obj is { code: number } => {
  return (
    obj !== null &&
    typeof obj === "object" &&
    "code" in obj &&
    typeof (obj as Record<string, unknown>).code === "number"
  );
};

// Type guard: Check if object is IUserProfile
const isUserProfile = (data: unknown): data is IUserProfile => {
  return (
    typeof data === "object" &&
    data !== null &&
    "profileId" in data &&
    "email" in data // or any other mandatory field
  );
};


export const useGetPublicProfile = (profileId: number | null) => {
  const [profile, setProfile] = useState<IUserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!profileId || isNaN(profileId)) {
      const errMsg = "[useGetPublicProfile] Invalid profileId";
      debugLogs(errMsg);
      setError(errMsg);
      setLoading(false);
      setProfile(null);
      return;
    }
    const fetchProfile = async () => {
      setLoading(true);
      setError(null);
      setProfile(null);
      try {
        const response = await getProfileById(profileId);
        const outerData = response.data;
        if (isApiError(outerData)) {
          const errMsg = outerData.error;
          setError(errMsg);
          debugLogs(errMsg);
          return;
        }
        if (hasCode(outerData) && outerData.code === 200) {
          const successResponse = outerData as ApiSuccessResponse<IUserProfileResponse>;
          const innerData = successResponse.data; 
          if (isUserProfile(innerData)) {
            setProfile(innerData);
          } else {
            const errMsg = "Invalid profile data";
            setError(errMsg);
            debugLogs(errMsg);
          }
        } else {
          const errMsg = "Unexpected response code or format";
          setError(errMsg);
          debugLogs(errMsg);
        }
      } catch (e) {
        const errMsg = "Fetch error";
        setError(errMsg);
        debugLogs(e);
      } finally {
        setLoading(false);
      }
    };
    fetchProfile();
  }, [profileId]);
  return { profile, loading, error };
};

export default useGetPublicProfile;
