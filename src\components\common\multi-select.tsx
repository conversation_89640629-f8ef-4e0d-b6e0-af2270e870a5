"use client";
import React, { useEffect, useState } from "react";
import { MultiSelectProps, Option } from "./_type";

const MultiSelect: React.FC<MultiSelectProps> = ({
  title,
  options,
  onChange,
  defaultSelected,
}) => {
  const [selectedOptions, setSelectedOptions] = useState<Option[]>(
    defaultSelected ?? []
  );
  const toggleOption = (option: Option) => {
    const alreadySelected = selectedOptions.some(
      (o) => o.value === option.value
    );
    const updated = alreadySelected
      ? selectedOptions.filter((o) => o.value !== option.value)
      : [...selectedOptions, option];

    setSelectedOptions(updated);
    onChange?.(updated);
  };
  const isSelected = (option: Option) =>
    selectedOptions.some((o) => o.value === option.value);

  useEffect(() => {
    if (defaultSelected) {
      setSelectedOptions(defaultSelected);
    }
  }, [defaultSelected]);

  return (
    <div className="w-full">
      <label className="block text-md font-regular text-(--full-black) mb-1">
        {title}
      </label>
      <div className="p-2 bg-white grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-2">
        {options.map((option) => (
          <div key={option.value} className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={isSelected(option)}
              onChange={() => toggleOption(option)}
              className="form-checkbox h-4 w-4 accent-(--violet-dark)"
            />
            <label className="text-md text-(--full-black)">
              {option.label}
            </label>
          </div>
        ))}
      </div>
    </div>
  );
};

export default MultiSelect;
