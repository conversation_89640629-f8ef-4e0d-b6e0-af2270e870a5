//_types
export { type CartItem } from "./common/_type";
export { type KennelRowType } from "./sections/profile/_type";

// common
// export { type ButtonProps } from "./common/_type";
export { default as Navbar } from "./common/navBar";
export { default as NextImagePlaceHolder } from "./common/next-image-placeholder";
export { default as StaticColorPolygon } from "./common/static-color-polygon";
export { default as DynamicColorPolygon } from "./common/dynamic-color-polygon";
export { default as Heading } from "./common/heading";
export { default as ImageCard } from "./common/image-card";
export { default as LottieAnimator } from "./common/lottie-animator";
export { default as Footer } from "./common/footer";
export { default as Button } from "./common/button";
export { default as Text } from "./common/text";
export { default as RingPulse } from "./common/ring-pulse";
export { default as BlurCanvas } from "./common/blur-canvas";
export { default as Avatar } from "./common/avatar";
export { default as Input } from "./common/input-field";
export { default as Textarea } from "./common/textarea";
export { default as DashboardLayout } from "./common/dashboard-profile";
export { default as DashboardHeader } from "./common/dashboardHeader";
export { default as Table } from "./common/table";
export { default as ImageButtonCard } from "./common/image-button-card";
export { default as PaginationBar } from "./common/pagination-bar";
export { default as ShoppingCart } from "./common/shopping-cart";
export { default as PopupModal } from "./common/popupmodal";
export { default as ImageCropModal } from "./common/image-crop-modal";
export { default as MultiSelect } from "./common/multi-select";
export { default as LoadingPAW } from "./common/loading-paw";
export { default as Slider } from "./common/slider";
export { default as ParentDataCard } from "./common/parent-data-card";
export { default as MasonryGallery } from "./common/masonry-grid";
export { default as AccordionItem } from "./common/accordion-item";
export { default as DropDown  } from "./common/drop-down";
export { default as ActionDropdown } from "./common/action-dropdown";
export { default as Popup } from "./common/popup";
export { default as ImageDataCard  } from "./common/image-data-card";

// layouts
export { default as SectionContainer } from "./layouts/section-container";
export { default as ContentContainer } from "./layouts/content-container";
export { default as HeadingContainer } from "./layouts/heading-container";
export { default as DataContainer } from "./layouts/data-container";

// sections
////////// auth page
export { default as AuthPageSignInSection } from "./sections/auth/sign-in-section";

////////// landing page
export { default as LandingPageHeroSection } from "./sections/landing/hero-section";
export { default as LandingPageFeaturedItemsSection } from "./sections/landing/featured-items-section";
export { default as LandingPageProductSection } from "./sections/landing/products-section";
export { default as LandingPageResponsibleBreederSection } from "./sections/landing/responsible-breeder-section";
export { default as LandingPageBreederProgramSection } from "./sections/landing/breeder-program-section";

//////// profile page
export { default as ProfileImageSection } from "./sections/profile/profile-image-section";
export { default as ProfileBioSection } from "./sections/profile/profile-bio-section";
export { default as ProfileKennelSection } from "./sections/profile/profile-kennel-section";

///////// animal profile page
export { default as AnimalDescriptionSection } from "./sections/animal-public-profile/animal-description-section";
export { default as AnimalDataSection } from "./sections/animal-public-profile/animal-data-section";
export { default as AnimalParentSection } from "./sections/animal-public-profile/animal-parent-section";
export { default as AnimalResultViewSection } from "./sections/animal-public-profile/animal-result-section";
export { default as AnimalCheckoutSection } from "./sections/animal-public-profile/animal-checkout-section";
export { default as AnimalProfileSection } from "./sections/animal-public-profile/animal-profile-section";

//////// animal page
export { default as AnimalPageImageSection } from "./sections/animal/animal-section";

///////// kennel page
export { default as KennelImageSection } from "./sections/kennel/kennel-image-section";
export { default as KennelBioSection } from "./sections/kennel/kennel-bio-section";
export { default as KennelAnimalSection } from "./sections/kennel/kennel-animal-section";
export { default as KennelDetailSection } from "./sections/kennel/kennel-detail-section";

////// kennel profile page
export { default as KennelBioDataSection } from "./sections/kennel-profile/kennel-bio-data-section";
export { default as AnimalBreedPageAnimalsSection } from "./sections/kennel-profile/animals-section";
export { default as AnimalDataViewSection } from "./sections/kennel-profile/animal-data-view-section";
export { default as KennelAdvanceSection } from "./sections/kennel-profile/kennel-advance-section";
export { default as KennelContactSection } from "./sections/kennel-profile/kennel-contact-section";
export { default as KennelProfileSection } from "./sections/kennel-profile/kennel-profile-section";

//////// public animal page
export { default as AnimalProfileBioSection } from "./sections/animal-profile/animal-bio-section";
export { default as AnimalProfileImageSection } from "./sections/animal-profile/animal-image-section";
export { default as AnimalResultSection } from "./sections/animal-profile/animal-result-section";

/////// 404 page
export { default as PageNotFound } from "./sections/404-page/not-found";

////// breeder profile
export { default as BreederProfileSection } from "./sections/breeder-public-profile/breeder-profile-section";
export { default as BreederContactSection } from "./sections/breeder-public-profile/breeder-contact-section";
export { default as BreederKennelSection } from "./sections/breeder-public-profile/breeder-kenel-section";
