import { useMemo } from "react";
import { HeadingProps } from "./_type";

const Heading = ({ lowContrast, type, title, classNames }: HeadingProps) => {
  const initCSS = useMemo(
    () =>
      (lowContrast ? "opacity-70\t" : "opacity-100\t").concat(
        `${classNames ?? ""}\t`
      ),
    [lowContrast, classNames]
  );

  let CSS = initCSS;

  switch (type) {
    case "Title":
      CSS += "text-3xl sm:text-4xl lg:text-5xl text-(--violet) font-semibold";
      break;
    case "Hx1":
      CSS += "text-2xl lg:text-3xl text-(--violet) font-semibold";
      break;
    case "Hx2":
      CSS += "text-xl lg:text-2xl text-(--violet) font-semibold";
    case "Hx3":
      CSS += "text-lg lg:text-xl text-(--violet) font-semibold";
      break;
  }

  return <span className={CSS}>{title}</span>;
};

export default Heading;
