/* eslint-disable @typescript-eslint/no-explicit-any */
import { isObject, mapKeys, mapValues } from "lodash";
import camelCase from "lodash/camelCase";

//convert keys to camel case and match api response with interface
export const convertKeysToCamelCase = (obj: any): any => {
  if (Array.isArray(obj)) {
    return obj.map((item) => convertKeysToCamelCase(item));
  } else if (isObject(obj)) {
    return mapValues(
      mapKeys(obj, (_, key) => camelCase(key)),
      convertKeysToCamelCase
    );
  }
  return obj;
};

//convert keys to title case
export const convertKeysTitleCase = (key: string): string => {
  return key
    .replace(/([a-z])([A-Z])/g, "$1 $2")
    .replace(/\b\w/g, (char) => char.toUpperCase());
};
