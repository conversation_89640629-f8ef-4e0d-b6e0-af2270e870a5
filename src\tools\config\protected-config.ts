"use server";

export const getServerConfig = async () => ({
  BASE_URL: process.env.NEXT_BASE_URL ?? "",
  X_API_KEY: process.env.NEXT_X_API ?? "",
  APP_MAX_AUTH_DAYS: parseInt(process.env.APP_MAX_AUTH_DAYS ?? "30") ?? 30,
  DEBUG_MODE: process.env.DEBUG_MODE ?? "",
  SSC_TOKENS: {
    httpOnly: true,
    sameSite: "strict" as boolean | "none" | "lax" | "strict" | undefined,
    secure: true,
    maxAge: 60 * 60 * 24 * 30, //default 30 days
  },
});
