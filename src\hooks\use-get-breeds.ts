import { ApiError } from "@/lib/_lib.type";
import { apiCommonError } from "@/lib/api-common-error";
import { debugLogs } from "@/lib/mode.debug";
import { getBreeds } from "@/services";
import { Breed, IGetBreedsResponse } from "@/services/_type";
import { useEffect, useState } from "react";

const useGetBreeds = (initLoading?: boolean) => {
  const [breeds, setBreeds] = useState<Breed[]>([]);
  const [breedLoading, setBreedLoading] = useState(false);

  useEffect(() => {
    if (initLoading) {
      callGetBreeds();
    }
  }, [initLoading]);

  const callGetBreeds = async () => {
    try {
      setBreedLoading(true);
      const breedData = await getBreeds();
      if (breedData.status === 200) {
        setBreeds((breedData.data as IGetBreedsResponse).data.results);
      } else {
        apiCommonError(breedData.status, (breedData.data as ApiError).error);
      }
      setBreedLoading(false);
    } catch (err) {
      debugLogs(err);
    }
  };

  return {
    callGetBreeds,
    breeds,
    breedLoading,
  };
};

export default useGetBreeds;
