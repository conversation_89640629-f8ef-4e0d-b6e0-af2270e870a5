import React from "react";
import { InputProps } from "./_type";

const Input: React.FC<InputProps> = ({
  label,
  error,
  className = "",
  disabled,
  ...props
}) => {
  return (
    <div className="w-full">
      {label && (
        <label
          htmlFor={props.id || props.name}
          className={"block mb-1 text-md font-regular"}
        >
          {label}
        </label>
      )}
      <input
        className={`w-full px-4 py-2 border rounded-lg  focus:outline-none focus:ring-1 focus:ring-(--sky-slate-dark) focus:border-transparent transition duration-200 ${
          error ? "border-red-500" : "border-gray-300"
        } ${className} ${disabled ? "opacity-70 pointer-events-none" : ""}`}
        {...props}
      />
      {error && <p className="mt-1 text-sm text-red-500">{error}</p>}
    </div>
  );
};

export default Input;
