"use client";

import {
  <PERSON><PERSON>,
  ContentContainer,
  ImageCard,
  LoadingPAW,
  PaginationBar,
  SectionContainer,
} from "@/components";
import { AdjustmentsHorizontalIcon } from "@heroicons/react/24/outline";
import { useEffect, useState } from "react";

const AnimalBreedPageAnimalsSection = () => {
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [loadingData, setLoadingData] = useState<boolean>(true);

  useEffect(() => {
    setTimeout(() => {
      setLoadingData(false);
    }, 3000);
  }, []);

  return (
    <SectionContainer background="Gray">
      <ContentContainer classNames="rounded-2xl min-h[300] w-full relative flex flex-col justify-between gap-6">
        {loadingData ? (
          <div className="flex justify-center items-center">
            <LoadingPAW />
          </div>
        ) : (
          <>
            <div>
              <Button title="Filters" contentEND>
                <AdjustmentsHorizontalIcon
                  className="mr-2"
                  width={24}
                  height={24}
                />
              </Button>
            </div>

            <div className="flex flex-col gap-8">
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-8  w-full min-h-60">
                <ImageCard />
                <ImageCard />
                <ImageCard />
                <ImageCard />
              </div>
              <div>
                <PaginationBar
                  currentPage={currentPage}
                  setCurrentPage={setCurrentPage}
                  totalPages={16}
                />
              </div>
            </div>
          </>
        )}
      </ContentContainer>
    </SectionContainer>
  );
};

export default AnimalBreedPageAnimalsSection;
