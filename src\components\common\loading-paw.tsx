"use client";

import <PERSON><PERSON><PERSON><PERSON>mat<PERSON> from "./lottie-animator";
import PA<PERSON>_JSON from "../../animations/paw.json";
import Heading from "./heading";
import { motion, AnimatePresence } from "framer-motion";

const LoadingPAW = () => {
  return (
    <div className="h-[240] w-[240] flex flex-col gap-8 relative items-center">
      <LottieAnimator json={PAW_JSON} />
      <AnimatePresence>
        <motion.div
          exit={{ opacity: 0 }}
          animate={{ opacity: [0, 1] }}
          className="w-full"
          transition={{
            repeat: Infinity,
            repeatType: "loop",
            duration: 1.45,
            ease: "easeIn",
          }}
        >
          <Heading
            classNames="absolute bottom-10 text-[#3F475E]!"
            title="Loading Please wait ...."
            type="Hx3"
          />
        </motion.div>
      </AnimatePresence>
    </div>
  );
};

export default LoadingPAW;
