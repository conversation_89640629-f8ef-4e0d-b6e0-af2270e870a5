import {
  ContentContainer,
  Heading,
  Text,
  NextImagePlaceHolder,
  SectionContainer,
  LottieAnimator,
} from "@/components";
import { healthPoints } from "@/constants/raw-objects";
import tickJSON from "../../../animations/tick.json";

const LandingPageBreederProgramSection = () => {
  return (
    <SectionContainer
      classNames="min-h-[500px]! relative z-50 flex justify-center"
      background="SkySlate"
    >
      <ContentContainer classNames="relative z-20 h-full">
        <div className="w-[100%] md:w-[40%]! flex flex-col gap-8">
          <Heading
            title="Key Components of the Orivet Responsible Breeder Program"
            type="Hx2"
            classNames="w-[80%]"
          />
          <div className="space-y-4">
            {healthPoints.map(({ title, description }, idx) => (
              <div key={idx} className="flex items-start gap-3">
                <div className="flex flex-col">
                  <div className="flex justify-start items-center">
                    <div className="h-14 w-14">
                      <LottieAnimator json={tickJSON} />
                    </div>
                    <Heading title={title} type="Hx3" />
                  </div>
                  <Text title={description} type="T3" />
                </div>
              </div>
            ))}
          </div>
        </div>
      </ContentContainer>
      <div className="flex justify-end items-end">
        <NextImagePlaceHolder
          height={500}
          width={500}
          classNames="w-[500px] absolute bottom-0 opacity-50 md:opacity-100 hidden sm:inline-block"
          src="/landing-page/banner.webp"
        />
      </div>
      {/* curves */}
      <div className="h-full w-4/6 md:w-2/3 absolute z-10 left-0 top-0 bg-(--gray) rounded-tr-full border-2 border-(--violet-light)" />
      {/*end of curves */}
    </SectionContainer>
  );
};

export default LandingPageBreederProgramSection;
