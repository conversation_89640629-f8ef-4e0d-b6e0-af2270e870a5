"use client";

import React, { useCallback, useState } from "react";
import { <PERSON><PERSON>ropdown, Button, LoadingPAW, Popup, Table } from "@/components";
import { useRouter } from "next/navigation";
import { USER_KENNEL_SECTION, ADD_KENNEL } from "@/constants/text-objects";
import {
  ConfirmPayload,
  KennelRowType,
  ProfileKennelSectionProps,
} from "./_type";
import { USER_PROFILE_URL_STATIC } from "@/constants/route-objects";
import { userDataStore } from "@/tools/state-lib/user.store";
import { actionColumns } from "@/constants/raw-objects";
import { useUpdateUserKennelStatus } from "@/hooks";

const ProfileKennelSection: React.FC<ProfileKennelSectionProps> = ({
  kennels,
  isLoadingKennels,
  updataMethod,
}) => {
  const userIDFromStore = userDataStore(
    (userState) => userState.userData?.user.id
  );
  const [openConfirmView, setOpenConfirmView] = useState(false);
  const [confirmPayload, setConfirmPayload] = useState<null | ConfirmPayload>(
    null
  );
  const [actionType, seActionType] = useState<
    null | "delete" | "visible" | "hide"
  >(null);
  const router = useRouter();
  const { callUpdateUserKennelStatus, updatingKennelStatus } =
    useUpdateUserKennelStatus();

  const handleClick = useCallback(() => {
    if (userIDFromStore) {
      router.push(
        USER_PROFILE_URL_STATIC.concat(`/${userIDFromStore}`.concat("/kennel"))
      );
    }
  }, [router, userIDFromStore]);

  const handleActionButtonClick = (
    type: "delete" | "visible" | "hide",
    id: number,
    visible: boolean
  ) => {
    let payload = {};
    if (type === "delete") {
      payload = {
        active: false,
      };
    } else if (type === "visible" || type === "hide") {
      payload = {
        visible: !visible,
      };
    }
    seActionType(type);
    setOpenConfirmView(true);
    setConfirmPayload({
      payload,
      id: id.toString(),
    });
  };

  const handleConfirmViewOnClick = useCallback(async () => {
    if (confirmPayload) {
      await callUpdateUserKennelStatus(
        confirmPayload.id,
        confirmPayload.payload
      );
      if (updataMethod) {
        await updataMethod();
      }
    }
    setOpenConfirmView(false);
  }, [callUpdateUserKennelStatus, confirmPayload, updataMethod]);

  const renderCell = (column: string, row: KennelRowType) => {
    if (column === "Actions") {
      return (
        <div className="flex gap-2 items-center">
          <ActionDropdown
            items={[
              {
                id: row.isVisible ? "hide" : "visible",
                name: row.isVisible ? "Hide" : "Visible",
                onClick: () =>
                  handleActionButtonClick("hide", row.id, row.isVisible),
              },
              {
                id: "delete",
                name: "Delete",
                onClick: () =>
                  handleActionButtonClick("delete", row.id, row.isVisible),
              },
            ]}
          />
        </div>
      );
    }
    return row[column as keyof KennelRowType];
  };

  return (
    <div className="flex flex-col p-4 border border-(--sky-slate-dark2) rounded-lg mt-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
        <div className="text-lg font-semibold">{USER_KENNEL_SECTION}</div>
        <div className="flex justify-end">
          <Button title={ADD_KENNEL} type="Secondary" onClick={handleClick} />
        </div>
      </div>
      {isLoadingKennels ? (
        <div className="flex justify-center items-center">
          <LoadingPAW />
        </div>
      ) : (
        <div className="mt-6 w-full relative justify-center items-center flex">
          {actionType && openConfirmView && (
            <Popup
              title={`Are you sure you want ${actionType} this kennel ?`}
              secondaryButtonTitle="close"
              primaryButtonTitle={actionType}
              onClickPrimary={() => handleConfirmViewOnClick()}
              onClickSecondary={() => setOpenConfirmView(false)}
              loadingPrimary={updatingKennelStatus}
            />
          )}
          <div className="w-full">
            <Table
              columns={actionColumns}
              data={kennels.map(({ name, address, isVisible, kennelId }) => ({
                id: kennelId,
                Kennel: name,
                City: address.city ?? "N/A",
                State: address.state ?? "N/A",
                isVisible,
                Actions: null,
              }))}
              renderCell={renderCell}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default ProfileKennelSection;
