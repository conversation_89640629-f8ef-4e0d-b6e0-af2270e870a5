"use client";

import React from "react";
import { Input, Button, Textarea } from "@/components";
import {
  animalInfo,
  animalData,
  animalAbout,
} from "@/constants/input-objects";
import { ANIMAL_PROFILE_TITLE,UPDATE_PET } from "@/constants/text-objects";

const AnimalProfileBioSection = () => {
  return (
    <div className="flex flex-col">
      <div className="text-lg text-(--full-black)">{ANIMAL_PROFILE_TITLE}</div>
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 mt-4">
        {animalInfo.map(({ id, label, placeholder }) => (
          <Input
            key={id}
            id={id}
            name={id}
            label={label}
            placeholder={placeholder}
          />
        ))}
      </div>
      {animalData.map(({ id, label, placeholder }) => (
        <div className="mt-2" key={id}>
          <Textarea id={id} name={id} label={label} placeholder={placeholder} />
        </div>
      ))}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 mt-4">
        {animalAbout.map(({ id, label, placeholder }) => (
          <Input
            key={id}
            id={id}
            name={id}
            label={label}
            placeholder={placeholder}
          />
        ))}
      </div>
      <Button title={UPDATE_PET} type="Secondary" classNames="mt-6 ml-auto" />
    </div>
  );
};

export default AnimalProfileBioSection;