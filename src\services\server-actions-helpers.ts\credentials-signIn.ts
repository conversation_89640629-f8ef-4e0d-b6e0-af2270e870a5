import {
  setAccessTokenCookie,
  setAuth<PERSON>ookie,
  setRefreshTokenCookie,
} from "@/tools/cookies";
import { ICredentials, ISignInResponse } from "../_type";
import signIn from "../public-api/sign-in";
import { debugLogs } from "@/lib/mode.debug";
import { ApiError } from "@/lib/_lib.type";

const credentialsSignIn = async (credentials: ICredentials) => {
  const response = await signIn(credentials);

  if (response.status === 400 || response.status === 428) {
    const error = (response.data as ApiError).error;
    return { error: error[0], status: response.status };
  }
  const authResponseBody = (response.data as ISignInResponse).data;

  // in case failed to generate tokens
  if (
    !authResponseBody?.token ||
    !authResponseBody?.token.accessToken ||
    !authResponseBody?.token.refreshToken
  ) {
    debugLogs("Tokens are missing in the response");
    throw new Error("Tokens are missing in the response");
  } else {
    await setAuthCookie({ user: authResponseBody.user });
    await setAccessTokenCookie(authResponseBody.token.accessToken);
    await setRefreshTokenCookie(authResponseBody.token.refreshToken);
  }
};

export default credentialsSignIn;
