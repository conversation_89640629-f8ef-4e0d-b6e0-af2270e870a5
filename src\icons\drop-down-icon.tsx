import React from "react";
import { DropdownIconProps } from "@/components/common/_type";

const DropdownIcon: React.FC<DropdownIconProps> = ({ isOpen, className = "" }) => {
  return (
    <svg
      className={`w-2.5 h-2.5 transition-transform duration-200 ${
        isOpen ? "rotate-180" : "rotate-0"
      } ${className}`}
      aria-hidden="true"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 10 6"
    >
      <path
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="2"
        d="m1 1 4 4 4-4"
      />
    </svg>
  );
};

export default DropdownIcon;