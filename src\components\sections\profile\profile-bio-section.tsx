"use client";

import React, { useCallback, useEffect, useMemo, useState } from "react";
import { Input, Button, Textarea } from "@/components";
import {
  USER_PROFILE_BIO_TITLE,
  SOCIAL_MEDIA_CONTACT_DETAILS,
} from "@/constants/text-objects";
import { PayloadItem, ProfileBioSectionProps } from "./_type";
import { ProfileDataField } from "@/hooks/_type";
import { IUserProfilePayload } from "@/services";

const ProfileBioSection = ({
  userData,
  updateMethod,
}: ProfileBioSectionProps) => {
  const profilePersonalInfo = useMemo(
    () => userData?.profilePersonalInfo,
    [userData]
  );
  const profileAboutFields = useMemo(
    () => userData?.profileAboutFields,
    [userData]
  );
  const profileContactFields = useMemo(
    () => userData?.profileContactFields,
    [userData]
  );

  const [localData, setLocalData] = useState({
    profilePersonalInfo,
    profileAboutFields,
    profileContactFields,
  });

  const [isEdit, setIsEdit] = useState<boolean>(false);
  const [saving, setSaving] = useState<boolean>(false);

  useEffect(() => {
    setLocalData({
      profilePersonalInfo,
      profileAboutFields,
      profileContactFields,
    });
  }, [profilePersonalInfo, profileAboutFields, profileContactFields]);

  const handleProfileDataUpdate = useCallback(async () => {
    setSaving(true);
    const { profileAboutFields, profileContactFields, profilePersonalInfo } =
      localData;
    const fullData = [
      profileAboutFields,
      profileContactFields,
      profilePersonalInfo,
    ].flat();
    const payload = (fullData as PayloadItem[]).reduce<
      Partial<IUserProfilePayload>
    >((acc, { id, value }) => {
      if (value !== undefined) acc[id] = value;
      return acc;
    }, {});
    await updateMethod(payload);
    setSaving(false);
  }, [localData, updateMethod]);

  if (
    !userData ||
    !profileAboutFields ||
    !profileContactFields ||
    !profilePersonalInfo
  ) {
    return null;
  }

  return (
    <div className="flex flex-col">
      <div className="text-lg text-(--full-black) flex items-center justify-between">
        {USER_PROFILE_BIO_TITLE}
        <Button
          loading={saving}
          disable={saving}
          type={isEdit ? "Primary" : "Secondary"}
          title={isEdit ? "Save" : "Edit"}
          onClick={async () => {
            if (isEdit) {
              await handleProfileDataUpdate();
            }
            setIsEdit((prv) => !prv);
          }}
        />
      </div>
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 mt-4">
        {localData.profilePersonalInfo &&
          localData.profilePersonalInfo.map(
            ({ id, label, placeholder, value }) => (
              <Input
                key={id}
                id={id}
                name={id}
                label={label}
                placeholder={placeholder}
                value={value}
                disabled={!isEdit || saving}
                onChange={(e) =>
                  setLocalData((prev) => ({
                    ...prev,
                    profilePersonalInfo: (
                      prev.profilePersonalInfo as ProfileDataField[]
                    ).map((field) =>
                      field.id === id
                        ? { ...field, value: e.target.value }
                        : field
                    ),
                  }))
                }
              />
            )
          )}
      </div>
      {localData.profileAboutFields &&
        localData.profileAboutFields.map(
          ({ id, label, placeholder, value }) => (
            <div className="mt-2" key={id}>
              <Textarea
                id={id}
                name={id}
                label={label}
                placeholder={placeholder}
                value={value}
                disabled={!isEdit || saving}
                onChange={(e) =>
                  setLocalData((prev) => ({
                    ...prev,
                    profileAboutFields: (
                      prev.profileAboutFields as ProfileDataField[]
                    ).map((field) =>
                      field.id === id
                        ? { ...field, value: e.target.value }
                        : field
                    ),
                  }))
                }
              />
            </div>
          )
        )}
      <div className="text-lg text-(--full-black) mt-4 mb-3">
        {SOCIAL_MEDIA_CONTACT_DETAILS}
      </div>
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
        {localData.profileContactFields &&
          localData.profileContactFields.map(
            ({ id, label, placeholder, value }) => (
              <Input
                key={id}
                id={id}
                name={id}
                label={label}
                placeholder={placeholder}
                value={value}
                disabled={!isEdit || saving}
                onChange={(e) =>
                  setLocalData((prev) => ({
                    ...prev,
                    profileContactFields: (
                      prev.profileContactFields as ProfileDataField[]
                    ).map((field) =>
                      field.id === id
                        ? { ...field, value: e.target.value }
                        : field
                    ),
                  }))
                }
              />
            )
          )}
      </div>
    </div>
  );
};

export default ProfileBioSection;
