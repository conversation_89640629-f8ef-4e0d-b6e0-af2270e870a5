"use server";

import server<PERSON>pi from "@/lib/server-api";
import { IUserKennelListResponse } from "../_type";
import { ApiError, ApiResponse } from "@/lib/_lib.type";

const getUserKennels = async (): Promise<
  ApiResponse<IUserKennelListResponse | ApiError>
> => {
  return await serverApi({
    url: "paw-print/my/kennels",
    method: "GET",
    isProtected: true,
  });
};

export default getUserKennels;
