export const SpeciesIcon = () => (
  <svg
    width="30"
    height="30"
    viewBox="0 0 30 30"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M25.7143 21.4191C24.9514 21.4233 24.2037 21.6331 23.55 22.0263L19.4646 17.9417C20.0467 17.0685 20.3573 16.0426 20.3573 14.9933C20.3573 13.9441 20.0467 12.9182 19.4646 12.045L22.1936 9.3173C22.8839 9.64966 23.6696 9.72708 24.4116 9.53582C25.1536 9.34456 25.8039 8.89698 26.2473 8.27236C26.6907 7.64774 26.8987 6.8864 26.8343 6.12321C26.7699 5.36002 26.4373 4.64424 25.8955 4.10267C25.3537 3.5611 24.6376 3.22869 23.8741 3.16433C23.1105 3.09997 22.3489 3.30782 21.724 3.75105C21.0991 4.19428 20.6513 4.84428 20.46 5.58592C20.2686 6.32755 20.3461 7.11294 20.6786 7.80297L17.9496 10.5307C17.076 9.94887 16.0498 9.63843 15 9.63843C13.9502 9.63843 12.924 9.94887 12.0504 10.5307L7.96393 6.44607C8.35715 5.79298 8.56701 5.04604 8.57143 4.28382C8.57143 3.43656 8.32008 2.60833 7.84916 1.90386C7.37824 1.19939 6.7089 0.650319 5.92579 0.326088C5.14268 0.0018561 4.28096 -0.0829777 3.44961 0.0823142C2.61827 0.247606 1.85463 0.6556 1.25526 1.2547C0.655891 1.8538 0.247716 2.61711 0.0823507 3.44808C-0.0830145 4.27906 0.00185692 5.14039 0.326232 5.92316C0.650608 6.70592 1.19992 7.37496 1.9047 7.84568C2.60948 8.31639 3.43808 8.56763 4.28572 8.56763C5.04864 8.5634 5.79631 8.35363 6.45 7.9604L10.5354 12.045C9.95329 12.9182 9.6427 13.9441 9.6427 14.9933C9.6427 16.0426 9.95329 17.0685 10.5354 17.9417L6.44893 22.0263C5.79555 21.6333 5.04828 21.4235 4.28572 21.4191C3.43808 21.4191 2.60948 21.6703 1.9047 22.141C1.19992 22.6117 0.650608 23.2808 0.326232 24.0635C0.00185692 24.8463 -0.0830145 25.7076 0.0823507 26.5386C0.247716 27.3696 0.655891 28.1329 1.25526 28.732C1.85463 29.3311 2.61827 29.7391 3.44961 29.9044C4.28096 30.0697 5.14268 29.9848 5.92579 29.6606C6.7089 29.3364 7.37824 28.7873 7.84916 28.0828C8.32008 27.3784 8.57143 26.5501 8.57143 25.7029C8.5672 24.9403 8.35733 24.193 7.96393 23.5396L12.0504 19.456C12.6206 19.8349 13.2581 20.101 13.9286 20.2399V23.758C13.2137 24.0107 12.6112 24.5078 12.2275 25.1615C11.8439 25.8152 11.7038 26.5835 11.832 27.3304C11.9602 28.0774 12.3485 28.7551 12.9282 29.2436C13.5079 29.7321 14.2418 30 15 30C15.7582 30 16.4921 29.7321 17.0718 29.2436C17.6515 28.7551 18.0398 28.0774 18.168 27.3304C18.2962 26.5835 18.1561 25.8152 17.7725 25.1615C17.3888 24.5078 16.7863 24.0107 16.0714 23.758V20.2399C16.7419 20.101 17.3794 19.8349 17.9496 19.456L22.0361 23.5406C21.6429 24.1937 21.433 24.9407 21.4286 25.7029C21.4286 26.5501 21.6799 27.3784 22.1508 28.0828C22.6218 28.7873 23.2911 29.3364 24.0742 29.6606C24.8573 29.9848 25.719 30.0697 26.5504 29.9044C27.3817 29.7391 28.1454 29.3311 28.7447 28.732C29.3441 28.1329 29.7523 27.3696 29.9176 26.5386C30.083 25.7076 29.9981 24.8463 29.6738 24.0635C29.3494 23.2808 28.8001 22.6117 28.0953 22.141C27.3905 21.6703 26.5619 21.4191 25.7143 21.4191ZM15 11.7805C15.6357 11.7805 16.2572 11.9689 16.7858 12.322C17.3143 12.675 17.7263 13.1768 17.9696 13.7638C18.2129 14.3509 18.2765 14.9969 18.1525 15.6201C18.0285 16.2434 17.7224 16.8159 17.2728 17.2652C16.8233 17.7145 16.2506 18.0205 15.6271 18.1445C15.0036 18.2684 14.3573 18.2048 13.7699 17.9616C13.1826 17.7185 12.6806 17.3067 12.3274 16.7783C11.9742 16.25 11.7857 15.6288 11.7857 14.9933C11.7866 14.1415 12.1255 13.3248 12.7281 12.7225C13.3307 12.1201 14.1478 11.7813 15 11.7805ZM2.14286 4.28382C2.14286 3.86019 2.26854 3.44607 2.504 3.09384C2.73946 2.7416 3.07412 2.46707 3.46568 2.30495C3.85724 2.14284 4.28809 2.10042 4.70377 2.18306C5.11944 2.26571 5.50126 2.46971 5.80094 2.76926C6.10063 3.06881 6.30472 3.45046 6.3874 3.86595C6.47008 4.28144 6.42765 4.71211 6.26546 5.10349C6.10327 5.49487 5.82861 5.82939 5.47622 6.06475C5.12383 6.3001 4.70953 6.42572 4.28572 6.42572C3.71739 6.42572 3.17235 6.20006 2.77049 5.79837C2.36862 5.39669 2.14286 4.85188 2.14286 4.28382ZM4.28572 27.8448C3.8619 27.8448 3.4476 27.7192 3.09521 27.4838C2.74282 27.2485 2.46816 26.9139 2.30597 26.5226C2.14379 26.1312 2.10135 25.7005 2.18403 25.285C2.26672 24.8695 2.4708 24.4879 2.77049 24.1883C3.07017 23.8888 3.45199 23.6848 3.86766 23.6021C4.28334 23.5195 4.7142 23.5619 5.10575 23.724C5.49731 23.8861 5.83198 24.1607 6.06744 24.5129C6.3029 24.8651 6.42857 25.2793 6.42857 25.7029C6.42857 26.2709 6.20281 26.8158 5.80094 27.2174C5.39908 27.6191 4.85404 27.8448 4.28572 27.8448ZM25.7143 27.8448C25.2905 27.8448 24.8762 27.7192 24.5238 27.4838C24.1714 27.2485 23.8967 26.9139 23.7345 26.5226C23.5724 26.1312 23.5299 25.7005 23.6126 25.285C23.6953 24.8695 23.8994 24.4879 24.1991 24.1883C24.4987 23.8888 24.8806 23.6848 25.2962 23.6021C25.7119 23.5195 26.1428 23.5619 26.5343 23.724C26.9259 23.8861 27.2605 24.1607 27.496 24.5129C27.7315 24.8651 27.8571 25.2793 27.8571 25.7029C27.8563 26.2707 27.6303 26.815 27.2286 27.2165C26.8269 27.618 26.2823 27.8439 25.7143 27.8448Z"
      fill="black"
    />
  </svg>
);

export const ColorIcon = () => (
  <svg
    width="30"
    height="30"
    viewBox="0 0 30 30"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M24.1667 15C23.5036 15 22.8677 14.7366 22.3989 14.2678C21.9301 13.7989 21.6667 13.163 21.6667 12.5C21.6667 11.837 21.9301 11.2011 22.3989 10.7322C22.8677 10.2634 23.5036 10 24.1667 10C24.8297 10 25.4656 10.2634 25.9344 10.7322C26.4033 11.2011 26.6667 11.837 26.6667 12.5C26.6667 13.163 26.4033 13.7989 25.9344 14.2678C25.4656 14.7366 24.8297 15 24.1667 15ZM19.1667 8.33333C18.5036 8.33333 17.8677 8.06994 17.3989 7.6011C16.9301 7.13226 16.6667 6.49637 16.6667 5.83333C16.6667 5.17029 16.9301 4.53441 17.3989 4.06557C17.8677 3.59673 18.5036 3.33333 19.1667 3.33333C19.8297 3.33333 20.4656 3.59673 20.9344 4.06557C21.4033 4.53441 21.6667 5.17029 21.6667 5.83333C21.6667 6.49637 21.4033 7.13226 20.9344 7.6011C20.4656 8.06994 19.8297 8.33333 19.1667 8.33333ZM10.8333 8.33333C10.1703 8.33333 9.53441 8.06994 9.06557 7.6011C8.59672 7.13226 8.33333 6.49637 8.33333 5.83333C8.33333 5.17029 8.59672 4.53441 9.06557 4.06557C9.53441 3.59673 10.1703 3.33333 10.8333 3.33333C11.4964 3.33333 12.1323 3.59673 12.6011 4.06557C13.0699 4.53441 13.3333 5.17029 13.3333 5.83333C13.3333 6.49637 13.0699 7.13226 12.6011 7.6011C12.1323 8.06994 11.4964 8.33333 10.8333 8.33333ZM5.83333 15C5.17029 15 4.53441 14.7366 4.06557 14.2678C3.59673 13.7989 3.33333 13.163 3.33333 12.5C3.33333 11.837 3.59673 11.2011 4.06557 10.7322C4.53441 10.2634 5.17029 10 5.83333 10C6.49637 10 7.13226 10.2634 7.6011 10.7322C8.06994 11.2011 8.33333 11.837 8.33333 12.5C8.33333 13.163 8.06994 13.7989 7.6011 14.2678C7.13226 14.7366 6.49637 15 5.83333 15ZM15 0C11.0218 0 7.20644 1.58035 4.3934 4.3934C1.58035 7.20644 0 11.0218 0 15C0 18.9782 1.58035 22.7936 4.3934 25.6066C7.20644 28.4196 11.0218 30 15 30C15.663 30 16.2989 29.7366 16.7678 29.2678C17.2366 28.7989 17.5 28.163 17.5 27.5C17.5 26.85 17.25 26.2667 16.85 25.8333C16.4667 25.3833 16.2167 24.8 16.2167 24.1667C16.2167 23.5036 16.4801 22.8677 16.9489 22.3989C17.4177 21.9301 18.0536 21.6667 18.7167 21.6667H21.6667C23.8768 21.6667 25.9964 20.7887 27.5592 19.2259C29.122 17.6631 30 15.5435 30 13.3333C30 5.96667 23.2833 0 15 0Z"
      fill="black"
    />
  </svg>
);

export const GenderIcon = () => (
  <svg
    width="34"
    height="35"
    viewBox="0 0 34 35"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M33.2756 3.34321e-05H27.2313C27.0854 -0.0013382 26.9424 0.0395249 26.8209 0.117315C26.6995 0.195104 26.6052 0.306225 26.5502 0.436243C26.4915 0.5667 26.4746 0.711037 26.5016 0.850798C26.5286 0.990559 26.5983 1.11939 26.7019 1.22083L28.1073 2.57263C28.2621 2.71555 28.2621 2.95227 28.1073 3.09519L24.9792 6.16206C24.9113 6.21052 24.8291 6.23669 24.7446 6.23669C24.6602 6.23669 24.578 6.21052 24.5101 6.16206C22.5466 4.96196 20.2123 4.45122 17.9022 4.71627C15.5921 4.98132 13.4482 6.00589 11.8333 7.61658C5.6419 6.65186 0.0216699 11.2358 0 17.2668C0.00305389 19.3548 0.706022 21.3862 2.00456 23.0594C3.3031 24.7327 5.12798 25.9586 7.20832 26.5552C7.3693 26.5999 7.47919 26.7428 7.47919 26.9036V28.0515C7.47919 28.1478 7.4394 28.2402 7.36858 28.3083C7.29775 28.3764 7.20169 28.4147 7.10152 28.4147H6.43595C6.1691 28.3753 5.89655 28.3919 5.63698 28.4633C5.37742 28.5347 5.13696 28.6592 4.9321 28.8283C4.72723 28.9974 4.5628 29.2071 4.45007 29.4431C4.33734 29.679 4.27897 29.9356 4.27897 30.1953C4.27897 30.4549 4.33734 30.7115 4.45007 30.9475C4.5628 31.1834 4.72723 31.3932 4.9321 31.5623C5.13696 31.7314 5.37742 31.8559 5.63698 31.9273C5.89655 31.9987 6.1691 32.0152 6.43595 31.9759H7.17891C7.38633 31.9759 7.55504 32.1396 7.55504 32.3391V33.0522C7.53401 33.3003 7.56667 33.5499 7.65094 33.7852C7.73521 34.0205 7.86927 34.2365 8.04466 34.4195C8.22005 34.6025 8.43297 34.7486 8.66998 34.8485C8.907 34.9484 9.16297 35 9.42174 35C9.68052 35 9.93649 34.9484 10.1735 34.8485C10.4105 34.7486 10.6234 34.6025 10.7988 34.4195C10.9742 34.2365 11.1083 34.0205 11.1925 33.7852C11.2768 33.5499 11.3095 33.3003 11.2884 33.0522V32.3391C11.2866 32.2542 11.3159 32.1713 11.371 32.1049C11.4262 32.0386 11.5037 31.9929 11.5903 31.9759H12.3456C12.8127 31.937 13.2476 31.7311 13.5645 31.3988C13.8813 31.0665 14.0571 30.632 14.0571 30.1811C14.0571 29.7302 13.8813 29.2957 13.5645 28.9635C13.2476 28.6312 12.8127 28.4253 12.3456 28.3864H11.5903C11.4913 28.3827 11.3975 28.3433 11.3275 28.276C11.2574 28.2086 11.2164 28.1183 11.2126 28.0232V27.1939C11.2157 27.1072 11.2509 27.0244 11.3119 26.9605C11.373 26.8966 11.4558 26.8558 11.5454 26.8455C13.7093 26.5323 15.7114 25.559 17.2569 24.069C23.439 24.9593 29.0175 20.4007 29.1042 14.389C29.1274 12.5425 28.6034 10.7279 27.5935 9.15746C27.5418 9.09561 27.5137 9.01867 27.5137 8.93936C27.5137 8.86004 27.5418 8.7831 27.5935 8.72125L30.782 5.65438C30.8166 5.61799 30.8587 5.58888 30.9056 5.5689C30.9525 5.54891 31.0032 5.53848 31.0544 5.53826C31.1566 5.54273 31.2541 5.58292 31.3253 5.65438L32.7153 6.9913C32.8209 7.0911 32.9546 7.15885 33.0999 7.18615C33.2452 7.21344 33.3956 7.19908 33.5325 7.14483C33.6693 7.09059 33.7866 6.99886 33.8697 6.88104C33.9529 6.76322 33.9982 6.62451 34 6.48214V0.66998C33.988 0.49161 33.907 0.324023 33.7729 0.20028C33.6388 0.0765361 33.4614 0.00562061 33.2756 0.00152221M19.6747 20.4811C20.1339 19.226 20.3212 17.8939 20.2251 16.5666C20.129 15.2393 19.7515 13.945 19.1159 12.7633C19.0034 12.5505 18.8482 12.3614 18.6592 12.2068C18.4702 12.0521 18.2511 11.9351 18.0148 11.8625C17.7784 11.7898 17.5294 11.763 17.2822 11.7835C17.035 11.8041 16.7945 11.8716 16.5746 11.9822C16.3547 12.0928 16.1598 12.2442 16.0012 12.4277C15.8425 12.6113 15.7233 12.8232 15.6505 13.0514C15.5776 13.2795 15.5525 13.5193 15.5767 13.7568C15.6008 13.9943 15.6737 14.2249 15.7911 14.4352C16.2864 15.3284 16.5341 16.3289 16.5171 17.3412C16.5109 22.0517 11.2064 24.9905 6.96686 22.6308C2.72731 20.2711 2.73504 14.383 6.97924 12.0308C7.78993 11.5819 8.69376 11.3109 9.62606 11.2373C9.22525 12.2453 9.00542 13.3116 8.97596 14.3905C8.97429 16.305 9.56306 18.1769 10.6678 19.7694C10.9552 20.1369 11.3778 20.3847 11.849 20.4622C12.3203 20.5396 12.8045 20.4408 13.2024 20.1859C13.6004 19.9311 13.8819 19.5395 13.9894 19.0914C14.0969 18.6434 14.0221 18.1727 13.7805 17.776C11.0656 13.856 13.7805 8.57833 18.667 8.27611C23.5536 7.97389 26.948 12.8734 24.7779 17.0956C24.2926 18.0393 23.5624 18.8465 22.6578 19.4393C21.7533 20.0321 20.705 20.3904 19.6143 20.4796L19.6747 20.4811Z"
      fill="black"
    />
  </svg>
);

export const BirthdayIcon = () => (
  <svg
    width="31"
    height="30"
    viewBox="0 0 31 30"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M14.8182 0C15.5 0.333333 16.8636 2.53333 16.8636 4C16.8636 5.46667 15.95 6 14.8182 6C13.6864 6 12.7727 5.8 12.7727 4.33333C12.7727 2.86667 14.1364 2 14.8182 0ZM24.3636 11.3333C27.7727 11.3333 30.5 14 30.5 17.3333C30.5 19.4133 29.4227 21.24 27.7727 22.32V30H3.22727V22.32C1.57727 21.24 0.5 19.4133 0.5 17.3333C0.5 14 3.22727 11.3333 6.63636 11.3333H12.7727V7.33333H16.8636V11.3333H24.3636ZM15.5 20.6667C16.4041 20.6667 17.2713 20.3155 17.9106 19.6904C18.5499 19.0652 18.9091 18.2174 18.9091 17.3333H20.9545C20.9545 18.2174 21.3137 19.0652 21.953 19.6904C22.5924 20.3155 23.4595 20.6667 24.3636 20.6667C25.2678 20.6667 26.1349 20.3155 26.7742 19.6904C27.4136 19.0652 27.7727 18.2174 27.7727 17.3333C27.7727 16.4493 27.4136 15.6014 26.7742 14.9763C26.1349 14.3512 25.2678 14 24.3636 14H6.63636C5.73222 14 4.8651 14.3512 4.22577 14.9763C3.58644 15.6014 3.22727 16.4493 3.22727 17.3333C3.22727 18.2174 3.58644 19.0652 4.22577 19.6904C4.8651 20.3155 5.73222 20.6667 6.63636 20.6667C7.54051 20.6667 8.40763 20.3155 9.04696 19.6904C9.68628 19.0652 10.0455 18.2174 10.0455 17.3333H12.0909C12.0909 18.2174 12.4501 19.0652 13.0894 19.6904C13.7287 20.3155 14.5959 20.6667 15.5 20.6667Z"
      fill="black"
    />
  </svg>
);

export const WeightIcon = () => (
  <svg
    width="33"
    height="30"
    viewBox="0 0 33 30"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M16.25 0C13.0304 0 10.3929 2.55249 10.3929 5.6682C10.3929 7.45244 11.2588 9.05088 12.6027 10.0922H7.10714L0.25 30H32.25L25.3929 10.0922H19.8973C21.2412 9.05088 22.1071 7.45244 22.1071 5.6682C22.1071 2.55249 19.4696 0 16.25 0ZM16.25 2.48848C18.0799 2.48848 19.5357 3.89737 19.5357 5.6682C19.5357 7.43903 18.0799 8.84793 16.25 8.84793C14.4201 8.84793 12.9643 7.43903 12.9643 5.6682C12.9643 3.89737 14.4201 2.48848 16.25 2.48848ZM10.3775 16.5173H12.0561V18.8166L14.4756 16.5172H16.4244L13.2899 19.4999L16.747 22.8161H14.6457L12.0561 20.3354V22.8161H10.3775V16.5173ZM18.9442 17.9855C19.2842 17.9854 19.5836 18.0501 19.8423 18.1794C20.1009 18.3088 20.3378 18.5127 20.553 18.7912V18.091H22.1224V22.3395C22.1224 23.0989 21.8739 23.6783 21.3769 24.0777C20.8828 24.4799 20.1649 24.681 19.2231 24.6809C18.9181 24.6809 18.6231 24.6585 18.3381 24.6135C18.0466 24.5669 17.7594 24.4978 17.4793 24.4068V23.2296C17.7526 23.3815 18.02 23.4941 18.2814 23.5672C18.5431 23.6432 18.8062 23.6811 19.0707 23.6811C19.5822 23.6811 19.9571 23.5728 20.1954 23.3562C20.4338 23.1397 20.553 22.8009 20.553 22.3396V22.0147C20.338 22.2903 20.101 22.4927 19.8423 22.6221C19.5836 22.7515 19.2843 22.8162 18.9442 22.8162C18.3484 22.8162 17.8557 22.5898 17.4664 22.137C17.0768 21.6814 16.8821 21.102 16.8821 20.3988C16.8821 19.6928 17.0768 19.1148 17.4664 18.6648C17.8557 18.212 18.3484 17.9855 18.9442 17.9855ZM19.5241 19.0739C19.2014 19.074 18.95 19.1893 18.7698 19.4199C18.5896 19.6506 18.4995 19.9769 18.4995 20.3988C18.4995 20.832 18.5867 21.161 18.7611 21.3859C18.9355 21.6082 19.1898 21.7193 19.5241 21.7193C19.8495 21.7193 20.1024 21.6039 20.2826 21.3733C20.4629 21.1426 20.553 20.8178 20.5529 20.3988C20.5529 19.9768 20.4628 19.6506 20.2826 19.42C20.1024 19.1893 19.8495 19.0739 19.524 19.0739H19.5241Z"
      fill="black"
    />
  </svg>
);
