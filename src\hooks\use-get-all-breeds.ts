"use server";

import serverApi from "@/lib/server-api";
import { ApiError, ApiResponse } from "@/lib/_lib.type";
import { IGetBreedsResponse, Breed } from "@/services/_type";
import { debugLogs } from "@/lib/mode.debug";

const getAllBreeds = async (): Promise<Breed[]> => {
  let allBreeds: Breed[] = [];
  let page = 1;
  let hasMore = true;

  while (hasMore) {
    try {
      const response: ApiResponse<IGetBreedsResponse | ApiError> =
        await serverApi({
          url: `animals/breeds?page=${page}&limit=50`,
          method: "GET",
        });

      if (response.status === 200) {
        const data = response.data as IGetBreedsResponse;
        const newBreeds = data.data.results;
        allBreeds = [...allBreeds, ...newBreeds];
        hasMore = newBreeds.length === 50;
        page++;
      } else {
        hasMore = false;
      }
    } catch (error) {
      hasMore = false;
      debugLogs(error);
    }
  }

  return allBreeds;
};

export default getAllBreeds;
