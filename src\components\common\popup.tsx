// "use client";

import React, { useMemo } from "react";
import Button from "./button";
import { PopUpPropsType } from "./_type";
// import { useAddAutoCloseEventForOutSideBoundary } from "@/hooks";

const Popup: React.FC<PopUpPropsType> = ({
  title,
  description,
  popUpType = "common",
  onClickPrimary,
  onClickSecondary,
  primaryButtonTitle,
  secondaryButtonTitle,
  loadingPrimary,
}) => {
  //   const ref = useRef<HTMLDivElement | null>(null);
  //   const { outSideBoundary } = useAddAutoCloseEventForOutSideBoundary(ref);

  const colorSelector = (type: "danger" | "Warning" | "common") => {
    let CSS = "";
    switch (type) {
      case "danger":
        CSS = "--red";
        break;
      case "Warning":
        CSS = "--orange";
        break;
      case "common":
        CSS = "--violet-medium-light";
        break;
      default:
        CSS = "--violet-medium-light";
    }
    return CSS;
  };

  const selectedColor = useMemo(() => colorSelector(popUpType), [popUpType]);

  // @todo:temp
  //   if (outSideBoundary) {
  //     return null;
  //   }

  return (
    <div
      className={`absolute rounded-lg overflow-hidden min-w-[280] sm:min-w-[360] shadow-(${selectedColor}) shadow-2xl`}
      //   ref={ref}
    >
      <div className="text-sm text-gray-500 dark:text-gray-400 w-full">
        <div
          className={`border-b border-gray-200 bg-(${selectedColor}) px-3 py-2`}
        >
          <h3 className="font-semibold text-(--black)  opacity-80 dark:text-white py-2">
            {title}
          </h3>
        </div>
        <div className="px-3 py-6 bg-(--gray) min-h-fit">
          {description && <p>{description}</p>}
          <div className="flex gap-4 justify-center">
            {primaryButtonTitle && (
              <Button
                title={primaryButtonTitle}
                type="Primary"
                buttonCSS
                classNames="w-fit h-[30]"
                onClick={onClickPrimary}
                loading={loadingPrimary}
              />
            )}
            {secondaryButtonTitle && (
              <Button
                title={secondaryButtonTitle}
                type="Secondary"
                buttonCSS
                classNames="w-[30] h-[30]"
                onClick={onClickSecondary}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Popup;
