import {
  ContentContainer,
  MasonryGallery,
  SectionContainer,
  ShoppingCart,
} from "@/components";

const AnimalCheckoutSection = () => {
  // @todo: Replace with real pet data from API
  const imageList = [
    "https://cdn.omlet.com/images/cache/438/512/Dog-Labrador_Retriever-A_healthy_adult_Labrador_sitting%2C_waiting_for_some_attention_from_it%27s_owner.jpg",
    "https://cdn.omlet.com/images/cache/438/512/Dog-Labrador_Retriever-A_healthy_adult_Labrador_sitting%2C_waiting_for_some_attention_from_it%27s_owner.jpg",
    "https://upload.wikimedia.org/wikipedia/commons/thumb/3/34/Labrador_on_Quantock_%282175262184%29.jpg/960px-Labrador_on_Quantock_%282175262184%29.jpg",
    "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRjEbdWarSZlyTmaPUc7bqQhkISYBqgQUxBBw&s",
    "https://www.puppygroep.nl/wp-content/uploads/2019/05/labrador-rasvereniging-groot.jpg",
    "https://www.fairpet.nl/wp-content/uploads/2021/02/Labrador_pup_AdobeStock_146204316-scaled-e1613756111946.jpeg",
    "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSbnZlSDgVtx1RSsaqa4IsiNPJdjG8ufrqoIOv10TP-7fNF8B9h2JmFBFsR9-rTBmwAwVg&usqp=CAU",
    "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQB2QEtZr4VfFqmRDq_Hv49YDCU6pQX_HomFaHRYB15tuujxAC4jsAcAo75FcjCCy3TuMQ&usqp=CAU",
    "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSfs6pAkkzqzsFy19TXOvmmECIPoCAW7nYqshrCEwIvNNKJ1R1PciM1ufOVgSu-bmqhp2U&usqp=CAU",
    "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTH_1iLhxjTwXmDxJFJ13dZlg1Chul6F9WHfiT_SwRRhk5yuoZ20Q4BoEY73wfDdFw-NJc&usqp=CAU",
    "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQAbgtIGBqJjzfOo0jA74iwKsG65U7bpahGWQ&s",
     "https://cdn.omlet.com/images/cache/438/512/Dog-Labrador_Retriever-A_healthy_adult_Labrador_sitting%2C_waiting_for_some_attention_from_it%27s_owner.jpg",
    "https://cdn.omlet.com/images/cache/438/512/Dog-Labrador_Retriever-A_healthy_adult_Labrador_sitting%2C_waiting_for_some_attention_from_it%27s_owner.jpg",
   
  
  ];
  return (
    <SectionContainer background="Gray">
      <ContentContainer classNames="w-full">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4">
          <div className="md:col-span-2 p-4" id="gallery-container">
            <MasonryGallery images={imageList} columns={{ base: 2, md: 4 }} />
          </div>
          <div className="md:col-span-1">
            <div className="sticky top-4" style={{ alignSelf: "start" }}>
              <ShoppingCart
                cartItems={[
                  { title: "French dogs", value: 44 },
                  { title: "French dogs", value: 56 },
                ]}
              />
            </div>
          </div>
        </div>
      </ContentContainer>
    </SectionContainer>
  );
};

export default AnimalCheckoutSection;
