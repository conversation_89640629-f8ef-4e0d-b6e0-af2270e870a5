import clsx from "clsx";

const BlurCanvas: React.FC<{
  children?: React.ReactNode;
  classNames: string;
}> = ({ children, classNames }) => {
  return (
    <div
      className={clsx(
        classNames,
        "fixed top-0 left-0 w-[100svw] h-[100svh] z-[100] bg-black/40 backdrop-blur-[6px] transition-all duration-300 ease-in-out"
      )}
      style={{
        backgroundImage: `
          linear-gradient(180deg, rgba(255, 255, 255, 0.037) 1px, transparent 1px),
          linear-gradient(90deg, rgba(255, 255, 255, 0.053) 1px, transparent 1px)
        `,
        backgroundSize: "20px 20px",
        WebkitBackdropFilter: "blur(0px)",
      }}
    >
      {children}
    </div>
  );
};

export default BlurCanvas;
