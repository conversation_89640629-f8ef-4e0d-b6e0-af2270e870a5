import {
  DashboardLayout,
  DashboardHeader,
  SectionContainer,
  ContentContainer,
  KennelImageSection,
  KennelBioSection,
  KennelAnimalSection,
  KennelDetailSection,
} from "@/components";

const KennelView = () => {
  return (
    <SectionContainer classNames="min-h-[calc(100svh_-_112px)] h-fit w-full">
      <ContentContainer classNames="relative z-20 h-full">
        <DashboardHeader title="Kennel" />
        <DashboardLayout
          section_one={<KennelImageSection />}
          section_two={<KennelBioSection />}
          section_three={<KennelDetailSection />}
        />
        <KennelAnimalSection />
      </ContentContainer>
    </SectionContainer>
  );
};

export default KennelView;
