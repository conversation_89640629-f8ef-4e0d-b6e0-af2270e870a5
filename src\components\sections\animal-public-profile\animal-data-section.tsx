import { ContentContainer, SectionContainer } from "@/components";
import { animalDataProps } from "@/components/layouts/_type";
import { ANIMAL_BREED, ANIMAL_REGISTRATION_NAME,ANIMAL_REGISTRATION_NUMBER, InfoItems,  } from "@/constants/text-objects";

const ShopAnimalSection = (props: animalDataProps) => {
  return (
    <SectionContainer background="Gray">
      <ContentContainer classNames="w-full">
        <div className="bg-white p-4 rounded-xl shadow text-center  mb-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 ">
            <div>
              <p className="text-sm text-gray-500">{ANIMAL_BREED}</p>
              <p className="text-md md:text-lg font-semibold">
                {props?.breed?.name || "Unknown"}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500 mt-2 md:mt-0">{ANIMAL_REGISTRATION_NAME}</p>
              <p className="text-md md:text-lg font-semibold">
                {props?.registrationName || "Unnamed"}
              </p>
            </div>

            <div>
              <p className="text-sm text-gray-500 mt-2 md:mt-0">{ANIMAL_REGISTRATION_NUMBER}</p>
              <p className="text-md md:text-lg font-semibold">
                {props?.registrationNumber || "No Registration Number"}
              </p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 md:grid-cols-5 gap-6">
          {InfoItems.map((item) => (
            <div
              key={item.key}
              className="flex flex-col items-center text-center p-4 bg-white rounded-xl shadow-md"
            >
              <div className="mb-2">{item.icon()}</div>
              <span className="mt-2 text-sm md:text-lg font-semibold">{item.label}</span>
              <span className="text-gray-700 text-sm md:text-lg">{item.getValue(props)}</span>
            </div>
          ))}
        </div>
      </ContentContainer>
    </SectionContainer>
  );
};

export default ShopAnimalSection;
