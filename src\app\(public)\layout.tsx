import "../globals.css";
import type { Metadata } from "next";
import { <PERSON>eist, <PERSON><PERSON><PERSON> } from "next/font/google";
import { Footer } from "@/components";
import NavBarNoSSR from "@/components/_no-ssr/navbar";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistPoppins = Poppins({
  variable: "--font-geist-poppins",
  weight: ["400", "500", "600", "800"],
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Paw Print Pedigree",
  description: "Paw Print Pedigree",
};

const RootLayout = ({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) => {
  return (
    <html lang="en">
      <body
        className={`${geistPoppins.variable} ${geistSans.variable} antialiased relative flex flex-col justify-between`}
      >
        <div className="flex justify-start flex-col h-[100svh] min-h-fit bg-(--sky-slate)">
          <NavBarNoSSR />
          <div className="mt-24 xl:mt-28 h-full">{children}</div>
          <Footer />
        </div>
      </body>
    </html>
  );
};

export default RootLayout;
