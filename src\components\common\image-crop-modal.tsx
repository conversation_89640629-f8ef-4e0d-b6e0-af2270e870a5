"use client";

import React, { useState, useCallback } from "react";
import <PERSON><PERSON><PERSON> from "react-easy-crop";
import { Button, PopupModal } from "@/components";
import { getCroppedImg } from "@/lib/crop-image";
import { ArrowUpTrayIcon } from "@heroicons/react/24/solid";
import { Area, ImageCropModalProps, MAX_IMAGES } from "./_type";

const ImageCropModal: React.FC<ImageCropModalProps> = ({
  isOpen,
  onClose,
  onSave,
}) => {
  const [files, setFiles] = useState<File[]>([]);
  const [croppedImages, setCroppedImages] = useState<string[]>([]);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [crop, setCrop] = useState({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<Area | null>(null);

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selected = Array.from(e.target.files || []);
    const allowed = MAX_IMAGES - croppedImages.length;
    if (selected.length > allowed) {
      alert(`You can only upload ${allowed} more image(s).`);
      return;
    }
    setFiles(selected);
    setCurrentImageIndex(0);
  };

  const onCropComplete = useCallback(
    (croppedArea: Area, croppedAreaPixels: Area) => {
      setCroppedAreaPixels(croppedAreaPixels);
    },
    []
  );

  const handleCrop = async () => {
    const file = files[currentImageIndex];
    if (!file || !croppedAreaPixels) return;
    const croppedImage = await getCroppedImg(
      URL.createObjectURL(file),
      croppedAreaPixels
    );
    const newCroppedImages = [...croppedImages, croppedImage];
    setCroppedImages(newCroppedImages);
    const remainingFiles = files.slice(currentImageIndex + 1);
    setFiles(remainingFiles);
    setCurrentImageIndex(0);
    setZoom(1);
    setCrop({ x: 0, y: 0 });
    if (remainingFiles.length === 0 || newCroppedImages.length >= MAX_IMAGES) {
      onSave(newCroppedImages);
      onClose();
    }
  };

  return (
    <PopupModal isOpen={isOpen} onClose={onClose} title="Crop Cover Image">
      <div className="space-y-4">
        <label
          style={{
            cursor: "pointer",
            padding: "8px 12px",
            display: "inline-flex",
            alignItems: "center",
            gap: "8px",
          }}
        >
          <ArrowUpTrayIcon style={{ width: 20, height: 20 }} />
          Choose Cover Image
          <input
            type="file"
            accept="image/*"
            multiple
            onChange={handleImageUpload}
            disabled={croppedImages.length >= MAX_IMAGES}
            style={{ display: "none" }}
          />
        </label>
        {files.length > 0 && (
          <div className="relative w-full h-64 bg-gray-200 rounded-md overflow-hidden">
            <Cropper
              image={URL.createObjectURL(files[currentImageIndex])}
              crop={crop}
              zoom={zoom}
              aspect={16 / 9}
              onCropChange={setCrop}
              onZoomChange={setZoom}
              onCropComplete={onCropComplete}
            />
          </div>
        )}
        <div className="flex justify-end">
          <Button onClick={handleCrop}>Crop & Save</Button>
        </div>
      </div>
    </PopupModal>
  );
};

export default ImageCropModal;
