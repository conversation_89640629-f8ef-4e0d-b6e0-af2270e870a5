import clsx from "clsx";
import { SectionContainerProps } from "./_type";

const DataContainer = ({
  children,
  classNames,
  background,
}: SectionContainerProps) => {
  let CSS = "bg-transparent";
  switch (background) {
    case "SkySlate":
      CSS = "bg-(--sky-slate)";
      break;
    case "White":
      CSS = "bg-white";
      break;
    case "Gray":
      CSS = "bg-(--gray)";
      break;
    default:
      CSS = "";
  }
  return (
    <div
      className={clsx(
        CSS,
        classNames,
        "h-fit w-full px-10 xl:px-36 pt-10 md:pt-12 pb-6"
      )}
    >
      {children}
    </div>
  );
};


export default DataContainer;
