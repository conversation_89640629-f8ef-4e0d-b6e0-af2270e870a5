import { KennelRowType } from "@/components";

export const polygonConfigs = [
  { classNames: "rounded-full md:blur-xs absolute top-[60px] left-[20%]" },
  { classNames: "rounded-full md:blur-xs absolute top-[130px] left-[65%]" },
  {
    classNames:
      "rounded-full md:blur-md w-16 h-16 absolute top-[210px] left-[35%]",
  },
  { classNames: "rounded-full md:blur-xs absolute top-[300px] left-[80%]" },
  {
    classNames:
      "rounded-full md:blur-md w-16 h-16 absolute top-[410px] left-[10%]",
  },
];

export const imageConfigs = [
  { src: "/dogs/d1.png", top: "top-[20px]", left: "left-[15%]" },
  { src: "/dogs/d2.png", top: "top-[160px]", left: "left-[85%]" },
  { src: "/dogs/d4.png", top: "top-[320px]", left: "left-[5%]" },
  {
    src: "/dogs/d5.png",
    top: "top-[450px] lg:top-[530px]",
    left: "left-[65%]",
  },
];

export const healthPoints = [
  {
    title: "Health Screening",
    description:
      "Genetic testing of breeding stock to identify breed-specific conditions, ensuring healthy progeny.",
  },
  {
    title: "Transparency",
    description:
      "Sharing genetic health results with prospective buyers via Orivet’s secure database.",
  },
  {
    title: "Standardized Collection Methods",
    description:
      "Utilizing approved techniques for collecting and submitting genetic samples.",
  },
  {
    title: "Genetic Health Utilization",
    description:
      "Employing genetic health insights to refine breeding strategies, aiming to eradicate prevalent diseases without compromising on key traits like temperament.",
  },
];

export const footerContent = [
  {
    heading: "Join our community",
    text: "Get involved with our community and join the conversation",
  },
  {
    heading: "Inclusion Policy",
    text: `We celebrate diversity and create an inclusive, respectful environment for all—valuing differences in culture, identity, and experience across our workplace, partners, and pet-loving community.`,
  },
  {
    heading: "First Nations Statement",
    text: `We acknowledge Traditional Custodians of the land, respect Elders past and present, and commit to learning from and supporting First Nations communities through inclusive, respectful, and collaborative practices.`,
  },
];

export const actionColumns: ("" | keyof KennelRowType)[] = [
  "Kennel",
  "City",
  "State",
  "Actions",
];
