{"name": "my-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --experimental-debug-memory-usage", "start": "next start", "lint": "next lint", "prepare": "husky"}, "dependencies": {"@heroicons/react": "^2.2.0", "clsx": "^2.1.1", "flowbite": "^3.1.2", "flowbite-react": "^0.12.5", "framer-motion": "^12.23.6", "husky": "^9.1.7", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "lottie-react": "^2.4.1", "next": "15.4.1", "react": "19.1.0", "react-dom": "19.1.0", "react-easy-crop": "^5.5.0", "reactjs-popup": "^2.0.6", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/lodash": "^4.17.20", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.1", "tailwindcss": "^4", "typescript": "^5"}}