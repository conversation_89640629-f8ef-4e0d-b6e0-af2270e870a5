"use client";
import DropdownIcon from "@/icons/drop-down-icon";
import { useState, useRef, useEffect } from "react";
import { DropdownProps } from "./_type";

const Dropdown: React.FC<DropdownProps> = ({
  options = ["No options"],
  defaultValue,
  onChange,
  width = "220px",
}) => {
  // Ensure selected has a valid default value
  const [selected, setSelected] = useState<string>(
    defaultValue ?? (options.length > 0 ? options[0] : "")
  );
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement | null>(null);
  const handleSelect = (value: string) => {
    setSelected(value);
    setIsOpen(false);
    if (onChange) onChange(value);
  };
  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div ref={dropdownRef} className="relative inline-block" style={{ width }}>
      <button
        type="button"
        onClick={() => setIsOpen((prev) => !prev)}
        className="flex items-center justify-between font-semibold sm:font-medium focus:outline-none w-full px-3 py-2 bg-white"
      >
        <span>{selected}</span>
        <DropdownIcon isOpen={isOpen} />
      </button>
      {isOpen && (
        <div className="absolute z-10 bg-white divide-y divide-gray-100 rounded-lg shadow mt-1 w-full">
          <ul className="py-2 text-sm text-gray-700">
            {options.map((option) => (
              <li key={option}>
                <button
                  onClick={() => handleSelect(option)}
                  className="block w-full px-4 py-2 text-left hover:bg-gray-100"
                >
                  {option}
                </button>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default Dropdown;