import { ProfileDataFieldObj } from "@/hooks/_type";
import { IUserKennel, IUserProfilePayload } from "@/services";

export type ProfileImageSectionProps = {
  firstName: string;
  lastName: string;
};

export type ProfileBioSectionProps = {
  userData: ProfileDataFieldObj | null;
  updateMethod: (updateUserData: IUserProfilePayload) => Promise<void>;
};

export type ProfileKennelSectionProps = {
  kennels: IUserKennel[];
  isLoadingKennels?: boolean;
  updataMethod?: () => Promise<void>;
};

export type PayloadItem = { id: keyof IUserProfilePayload; value?: string };

export type KennelRowType = {
  id: number;
  Kennel: string;
  City: string;
  State: string;
  Actions: null;
  isVisible: boolean;
};

export type ConfirmPayload = {
  payload: object;
  id: string;
};
