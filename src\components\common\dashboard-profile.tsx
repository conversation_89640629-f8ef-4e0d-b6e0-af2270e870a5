"use client";
import React from "react";
import { DashboardLayoutProps } from "./_type";

const DashboardLayout: React.FC<DashboardLayoutProps> = ({
  section_one,
  section_two,
  section_three,
}) => {
  return (
    <div>
      <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
        <div className="border border-(--sky-slate-dark2) rounded-lg w-full">
          {section_one}
        </div>
        <div className="md:col-span-2 border border-(--sky-slate-dark2) p-4 rounded-lg w-full">
          {section_two}
        </div>
      </div>
      {section_three ? (
        <div className="mt-4 border border-(--sky-slate-dark2) p-4 rounded-lg w-full">
          {section_three}
        </div>
      ) : null}
    </div>
  );
};

export default DashboardLayout;
