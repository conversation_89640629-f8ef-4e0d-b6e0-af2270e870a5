"use client";

import Link from "next/link";
import React, { useEffect, useRef, useState } from "react";
import { AdjustmentsHorizontalIcon } from "@heroicons/react/24/outline";
import { ActionDropdownPropsType } from "./_type";
import { useAddAutoCloseEventForOutSideBoundary } from "@/hooks";

const ActionDropdown: React.FC<ActionDropdownPropsType> = ({ items }) => {
  const [open, setOpen] = useState(false);
  const ref = useRef<HTMLDivElement | null>(null);

  const { outSideBoundary } = useAddAutoCloseEventForOutSideBoundary(ref);

  useEffect(() => {
    if (outSideBoundary) {
      setOpen(false);
    }
  }, [outSideBoundary]);

  return (
    <div className="relative" ref={ref}>
      <button
        onClick={() => setOpen((prv) => !prv)}
        className="h-8 w-8 inline-flex items-center justify-center cursor-pointer p-2 text-sm font-medium text-center text-gray-900 bg-white rounded-lg hover:bg-gray-100 focus:ring-4 focus:outline-none dark:text-white focus:ring-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 dark:focus:ring-gray-600"
        type="button"
      >
        <AdjustmentsHorizontalIcon />
      </button>

      <div
        className={`z-10 ${
          open ? "absolute top-10" : "hidden "
        } bg-white divide-y divide-gray-100 rounded-lg shadow-sm w-44 dark:bg-gray-700 dark:divide-gray-600`}
      >
        <ul className="py-2 text-sm text-gray-700 dark:text-gray-200">
          {items.map(({ id, name, onClick, href }) => (
            <li key={id}>
              {href ? (
                <Link
                  href={href}
                  onClick={onClick}
                  className="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white cursor-pointer"
                >
                  {name}
                </Link>
              ) : (
                <span
                  onClick={onClick}
                  className="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white cursor-pointer"
                >
                  {name}
                </span>
              )}
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default ActionDropdown;
