// types
export {
  type ICredentials,
  type IBaseResponse,
  type IGetUserDataResponse,
  type IUserProfile,
  type IUserKennel,
  type IUserKennelListResponse,
  type IUserProfilePayload,
  type IKennelUpdatePayload,
  type SaveKennelPayload,
} from "./_type";

// public
///////// POST
export { default as signIn } from "./public-api/sign-in";
export { default as getNewTokens } from "./public-api/get-new-tokens";
export { default as saveUserKennels } from "./private-api/save-user-kennel";

///////// GET
export { default as getBreeds } from "./public-api/get-breeds";
export { default as getAnimalById } from "./public-api/get-animal";
export { default as getKennelById } from "./public-api/get-kennel";
export { default as getResultsByAnimalId } from "./public-api/get-result";
export { default as getKennelByUserId } from "./public-api/get-user-kennel";


// private
////////// POST

///////// GET
export { default as getUserData } from "./private-api/get-user-data";
export { default as getUserKennels } from "./private-api/get-user-kennels";
export { default as getUserAnimals } from "./private-api/get-user-animals";

//////// PATCH
export { default as updateUserKennelStatus } from "./private-api/update-user-kennel-status";

// server helpers
export { default as credentialsSignIn } from "./server-actions-helpers.ts/credentials-signIn";
