import { footerContent } from "@/constants/raw-objects";
import Heading from "./heading";
import NextImagePlaceHolder from "./next-image-placeholder";
import Text from "./text";

const Footer = () => {
  return (
    <div className="bg-(--violet-dark2) text-white w-full px-10 xl:px-36 py-10 md:py-18 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-5">
      <div className="flex justify-start items-start -ml-4 -mt-4">
        <NextImagePlaceHolder src="/logo-type2.svg" width={200} height={200} />
      </div>

      {footerContent.map((item, index) => (
        <div key={index} className="flex flex-col gap-1">
          <Heading
            title={item.heading}
            type={"Hx2"}
            classNames={"text-white"}
          />
          <Text title={item.text} type={"T4"} classNames={"text-white"} />
        </div>
      ))}
    </div>
  );
};

export default Footer;
