// AnimalDataViewSection.tsx
"use client";

import { ContentContainer, AnimalBreedPageAnimalsSection } from "@/components";
import { AnimalDataViewSectionProps } from "@/services/_type";

const AnimalDataViewSection = ({ data }: AnimalDataViewSectionProps) => {
  const parents = data.filter((animal) => animal.type === "parent");
  const puppies = data.filter((animal) => animal.type === "puppy");

  return (
    <ContentContainer>
      <div>
        <AnimalBreedPageAnimalsSection title="Puppie(s)" animals={puppies}/>
        <AnimalBreedPageAnimalsSection title="Parent(s)" animals={parents}  />
      </div>
    </ContentContainer>
  );
};

export default AnimalDataViewSection;
