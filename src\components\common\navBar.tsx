"use client";

import { AnimatePresence, motion } from "framer-motion";
import { useState } from "react";
import Button from "./button";
import NextImagePlaceHolder from "./next-image-placeholder";
import BlurCanvas from "./blur-canvas";
import { PUBLIC_URLS, SIGN_IN_PAGE_URL } from "@/constants/route-objects";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import clsx from "clsx";
import { useAuthNavigate } from "@/hooks";
import { useClearData } from "@/hooks";

const navItem = (
  title: string,
  href: string,
  activeLink: boolean,
  index: number
) => {
  return (
    <motion.div
      key={title}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.125, duration: 0.25 }}
      className="relative font-medium text-[--black] w-full md:w-fit min-h-10 flex items-center justify-start py-6 md:py-2 cursor-pointer px-22 md:px-0 group"
    >
      <Link href={href}>
        <span className="text-xl md:text-base relative z-10">{title}</span>
        <span
          className={clsx(
            "absolute left-1/2 bottom-0 h-[2px] bg-(--black) w-0 md:group-hover:w-full transition-all duration-300 ease-in-out transform -translate-x-1/2 origin-center",
            activeLink ? "w-full! bg-(--orange)! h-[3px]!" : ""
          )}
        />
      </Link>
    </motion.div>
  );
};

const NavBar = () => {
  const [showMobileNav, setShowMobileNav] = useState(false);
  const path = usePathname();
  const navigation = useRouter();
  const { isAuth } = useAuthNavigate();
  const { clearPersistData } = useClearData();

  const handleOnclickNavigationButton = async (url: string) => {
    navigation.push(url);
  };

  return (
    <div>
      {showMobileNav && <BlurCanvas classNames="md:hidden" />}
      <div className="flex fixed h-24 xl:h-28 bg-(--sky-slate-dark2) md:bg-(--sky-slate) top-0 w-full justify-between items-center px-10 xl:px-36 border-b-1 md:border-0 border-(--sky-slate-dark) z-[100]">
        <div onClick={() => setShowMobileNav((prv) => !prv)}>
          <NextImagePlaceHolder
            src="/logo.png"
            width={120}
            height={48}
            classNames="w-20 xl:w-[120]"
          />
        </div>
        {/* desktop */}
        <div className="hidden md:flex  gap-8 xl:gap-12">
          {PUBLIC_URLS.map(({ url, name }, index) =>
            navItem(name, url, path === url, index)
          )}
        </div>

        <div className="hidden md:flex gap-4">
          <Button
            title={isAuth ? "Sign out" : "Sign in"}
            onClick={async () => {
              if (isAuth) {
                await clearPersistData();
              }
              handleOnclickNavigationButton(SIGN_IN_PAGE_URL);
            }}
            type={isAuth ? "type2Primary" : "Primary"}
          />
        </div>
      </div>
      {/* mobile */}
      <AnimatePresence>
        {showMobileNav && (
          <>
            <motion.div
              key="mobile-menu"
              initial={{ opacity: 0, y: -12 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -12 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
              className="flex md:hidden flex-col  top-24 fixed w-full z-[100] md:px-22 overflow-hidden"
            >
              <div className="bg-(--sky-slate-dark2) md:bg-(--sky-slate)">
                {PUBLIC_URLS.map(({ url, name }, index) =>
                  navItem(name, url, path === url, index)
                )}
              </div>
              <div className="h-[100] bg-(--sky-slate-dark2) md:hidden rounded-b-full w-full -mt-1 border border-t-0 border-(--sky-slate-dark)" />
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
};

export default NavBar;

// xl 36 px h-32 items gap-12
// md px-22 h-24 gap-8
