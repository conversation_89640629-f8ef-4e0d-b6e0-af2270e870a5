"use client";

import React, { useEffect, useState } from "react";
import Popup from "reactjs-popup";
import "reactjs-popup/dist/index.css";
import { PopupModalProps } from "./_type";
import { XMarkIcon } from "@heroicons/react/24/solid";

const PopupModal: React.FC<PopupModalProps> = ({
  title,
  isOpen,
  onClose,
  children,
  footer,
  customStyles = "",
}) => {
  const [contentStyle, setContentStyle] = useState({
    width: "280px",
    borderRadius: "0.5rem",
    background: "#fff",
    padding: 0,
    overflow: "hidden",
  });

  useEffect(() => {
    const updateWidth = () => {
      const screenWidth = window.innerWidth;
      let width = "280px"; 
      if (screenWidth >= 1024) {
        width = "680px"; 
      } else if (screenWidth >= 640) {
        width = "450px"; 
      }
      setContentStyle((prev) => ({
        ...prev,
        width,
      }));
    };
    updateWidth();
    window.addEventListener("resize", updateWidth);
    return () => window.removeEventListener("resize", updateWidth);
  }, []);

  return (
    <Popup
      open={isOpen}
      modal
      nested
      closeOnDocumentClick
      onClose={onClose}
      contentStyle={contentStyle}
    >
      <div className={`p-3 w-full ${customStyles} relative`}>
        <XMarkIcon
          className="absolute top-4 right-4 text-gray-500 hover:text-gray-800 text-xl font-bold w-5 h-5 cursor-pointer"
          aria-label="Close modal"
          onClick={onClose}
        />
        {title && <h2 className="text-xl font-semibold mb-4">{title}</h2>}
        <div className="text-gray-700 overflow-y-auto max-h-[60vh] pr-1">
          {children}
        </div>
        {footer && (
          <div className="pt-4 border-t border-(--sky-slate-dark2) mt-4 flex justify-end">
            {footer}
          </div>
        )}
      </div>
    </Popup>
  );
};

export default PopupModal;
