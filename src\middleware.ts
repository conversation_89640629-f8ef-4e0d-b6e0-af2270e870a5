import { NextResponse, NextRequest } from "next/server";
import {
  BASE_PAGE_URL,
  SIGN_IN_PAGE_URL,
  USER_PROFILE_URL_STATIC,
} from "./constants/route-objects";

export async function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;
  const refreshToken = request.cookies.get("refresh-token");
  const accessToken = request.cookies.get("access-token");
  const isAuthUser = !!accessToken || !!refreshToken;

  if (pathname === SIGN_IN_PAGE_URL && isAuthUser) {
    return NextResponse.redirect(new URL(BASE_PAGE_URL, request.url));
  }

  // protected routes
  if (pathname.startsWith(USER_PROFILE_URL_STATIC)) {
    if (!isAuthUser) {
      // no auth user login redirect
      return NextResponse.redirect(new URL(SIGN_IN_PAGE_URL, request.url));
    }
  }
  return NextResponse.next();
}
