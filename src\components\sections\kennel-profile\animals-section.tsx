// AnimalBreedPageAnimalsSection.tsx
"use client";

import {
  Button,
  ContentContainer,
  DataContainer,
  ImageCard,
  LoadingPAW,
  PaginationBar,
} from "@/components";
import { AdjustmentsHorizontalIcon } from "@heroicons/react/24/outline";
import { useEffect, useState } from "react";
import { AnimalBreedPageAnimalsSectionProps } from "@/services/_type";
import { useRouter } from "next/navigation";

const AnimalBreedPageAnimalsSection = ({ animals, title }: AnimalBreedPageAnimalsSectionProps) => {
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [loadingData, setLoadingData] = useState<boolean>(true);
  const router = useRouter(); 

  useEffect(() => {
    setTimeout(() => {
      setLoadingData(false);
    }, 500);
  }, []);

  if (loadingData) return <LoadingPAW />;

  return (
    <DataContainer>
      <ContentContainer classNames="rounded-2xl w-full relative flex flex-col justify-between gap-6 mt-6">
        <>
          <div className="grid grid-cols-2 items-center">
            <div className="text-2xl font-semibold">
              {animals.length} {title.toLowerCase()} available
            </div>
            <div className="flex justify-end">
              <Button title="Filters" contentEND>
                <AdjustmentsHorizontalIcon
                  className="mr-2"
                  width={24}
                  height={24}
                />
              </Button>
            </div>
          </div>
          <div className="flex flex-col gap-8">
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-8 w-full min-h-60">
              {animals.map((animal) => (
                <ImageCard
                  key={animal.id}
                  title={animal.name}
                  price={animal.price}
                  onClick={() => router.push(`/kennel/3/animal/${animal.id}`)}
                />
              ))}
            </div>
            <PaginationBar
              currentPage={currentPage}
              setCurrentPage={setCurrentPage}
              totalPages={1}
            />
          </div>
        </>
      </ContentContainer>
    </DataContainer>
  );
};


export default AnimalBreedPageAnimalsSection;