"use server";

import server<PERSON><PERSON> from "@/lib/server-api";
import { ICredentials, ISignInResponse } from "../_type";
import { ApiError, ApiResponse } from "@/lib/_lib.type";

const signIn = async (
  credentials: ICredentials
): Promise<ApiResponse<ISignInResponse | ApiError>> => {
  return await serverApi({
    url: "sign-in/",
    body: JSON.stringify({
      username: credentials.username,
      password: credentials.password,
    }),
  });
};

export default signIn;
