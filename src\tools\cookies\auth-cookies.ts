"use server";

import { jwtDecode } from "jwt-decode";
import { cookies } from "next/headers";
import { IAuthSession } from "./_type";
import { getServerConfig } from "../config/protected-config";

// add cookies
export const setAccessTokenCookie = async (accessToken: string) => {
  const decoded = jwtDecode(accessToken);

  if (decoded && decoded?.exp) {
    const SSC = await cookies();
    const cookieConfig = (await getServerConfig()).SSC_TOKENS;
    const currentTimeInSeconds = Math.floor(Date.now() / 1000);
    SSC.set("access-token", accessToken, {
      ...cookieConfig,
      maxAge: decoded.exp - currentTimeInSeconds,
    });
    console.debug({ accessTokenExp: decoded.exp - currentTimeInSeconds });
  }
};

export const setRefreshTokenCookie = async (refreshToken: string) => {
  const cookieConfig = (await getServerConfig()).SSC_TOKENS;
  const SSC = await cookies();
  SSC.set("refresh-token", refreshToken, { ...cookieConfig });
};

export const setAuthCookie = async (authData: IAuthSession | null) => {
  const cookieConfig = (await getServerConfig()).SSC_TOKENS;
  const SSC = await cookies();
  SSC.set("auth-cookie", JSON.stringify(authData), {
    ...cookieConfig,
  });
};

// get cookies
export const getAuthCookie = async (): Promise<IAuthSession | null> => {
  const SSC = await cookies();
  const authData = SSC.get("auth-cookie")?.value;
  if (authData) {
    return JSON.parse(authData);
  } else {
    return null;
  }
};

export const getAccessCookie = async (): Promise<string | null> => {
  const SSC = await cookies();
  return SSC.get("access-token")?.value ?? null;
};

export const getRefreshCookie = async (): Promise<string | null> => {
  const SSC = await cookies();
  return SSC.get("refresh-token")?.value ?? null;
};

// delete
export const removeAuthCookie = async () => {
  const SSC = await cookies();
  SSC.delete("auth-cookie");
};

export const removeAccessCookie = async () => {
  const SSC = await cookies();
  SSC.delete("access-token");
};

export const removeRefreshCookie = async () => {
  const SSC = await cookies();
  SSC.delete("refresh-token");
};

export const clearCookies = async () => {
  await removeAuthCookie();
  await removeAccessCookie();
  await removeRefreshCookie();
};
