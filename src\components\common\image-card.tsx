import { ImageCardProps } from "./_type";
import NextImagePlaceHolder from "./next-image-placeholder";

const ImageCard = ({
  imageSrc,
  title,
  description,
  price,
  onClick,
}: ImageCardProps) => {
  return (
    <div
      onClick={onClick}
      className={`w-full h-full min-h-[12rem] rounded-xl overflow-hidden bg-white transition-shadow duration-300 ${
        onClick ? "cursor-pointer hover:shadow-lg" : ""
      }`}
    >
      <div className={`w-full overflow-hidden`}>
        <NextImagePlaceHolder
          height={600}
          width={600}
          src={imageSrc || "/temp.jpg"}
          classNames="w-full h-full object-cover"
        />
      </div>
      <div>
        {title && (
          <div className="mt-2 text-lg font-semibold text-gray-800 px-4 pt-3">
            {title}
          </div>
        )}
        {price !== undefined && (
          <div className="text-md text-gray-600 px-4 pb-3">AUD {price}</div>
        )}
        {description && (
          <div className="text-sm text-gray-500 mt-2 px-4 pb-3">
            {description}
          </div>
        )}
      </div>
    </div>
  );
};

export default ImageCard;
