"use client";

import React, { useState } from "react";
import { Avatar, Button, ImageButtonCard, ImageCropModal } from "@/components";
import { TrashIcon, PlusCircleIcon } from "@heroicons/react/24/outline";
import { EDIT, EDIT_IMAGE, COVER_IMAGE } from "@/constants/text-objects";
import { ProfileImageSectionProps } from "./_type";

const ProfileImageSection = ({
  firstName,
  lastName,
}: ProfileImageSectionProps) => {
  const [isCropOpen, setIsCropOpen] = useState(false);
  const [coverImages, setCoverImages] = useState<string[]>([]);
  const handleSaveCrops = (images: string[]) => {
    setCoverImages(images);
  };

  return (
    <div className="flex flex-col p-4">
      <div className="text-xl font-semibold">
        {firstName}
        {`\t${lastName}`}
      </div>
      <div className="grid grid-cols-2 gap-3 md:gap-0 items-center mt-4">
        <div>
          <Avatar src="/temp.jpg" size={32} alt="User" />
        </div>
        <div>
          <Button type="Secondary">
            <span className="block md:hidden">{EDIT}</span>
            <span className="hidden md:block">{EDIT_IMAGE}</span>
          </Button>
        </div>
      </div>
      <div className="flex items-center justify-between text-lg text-(--full-black) mt-8 pt-4 mb-3 border-t border-dashed border-(--sky-slate-dark2)">
        <span>{COVER_IMAGE}</span>
        <PlusCircleIcon
          className="h-9 w-9 text-(--violet)/80"
          onClick={() => setIsCropOpen(true)}
        />
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
        {coverImages.map((imgSrc, index) => (
          <div
            key={index.toLocaleString().concat(Math.random().toLocaleString())}
            className="relative"
          >
            <ImageButtonCard
              imgSrc={imgSrc}
              alt={`Cover ${index + 1}`}
              onFirstButtonClick={() => console.log("Edit")}
              onSecondButtonClick={() => console.log("Delete")}
              //firstButtonIcon={""}
              secondButtonIcon={<TrashIcon className="h-6 w-6 text-red-500" />}
            />
          </div>
        ))}
      </div>
      <ImageCropModal
        isOpen={isCropOpen}
        onClose={() => setIsCropOpen(false)}
        onSave={handleSaveCrops}
      />
    </div>
  );
};

export default ProfileImageSection;
