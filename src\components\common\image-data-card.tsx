import { ImageDataCardProps } from "./_type";
import Button from "./button";
import NextImagePlaceHolder from "./next-image-placeholder";

const ImageDataCard = ({ imageSrc, title, onClick }: ImageDataCardProps) => {
  return (
    <div
      onClick={onClick}
      className={`group w-full h-full min-h-[12rem] rounded-xl overflow-hidden bg-white transition-shadow duration-300 ${
        onClick ? "cursor-pointer hover:shadow-lg" : ""
      }`}
    >
      <div className="w-full overflow-hidden relative">
        <NextImagePlaceHolder
          height={600}
          width={400}
          src={imageSrc || "/temp.jpg"}
          classNames="w-full h-full object-cover"
        />
        {title && (
          <div
            className="absolute bottom-0 w-[95%] bg-white/85 px-3 m-2 rounded-lg
                       overflow-hidden transition-all duration-300
                       max-h-[6rem] lg:max-h-[2.4rem] lg:group-hover:max-h-20 p-2"
          >
            <div className="text-[14px] lg:text-[15px] font-semibold text-gray-900">
              {title}
            </div>
            <div
              className="  text-white text-sm rounded
             opacity-100 lg:opacity-0 lg:group-hover:opacity-100
             transition-opacity duration-300"
            >
              <Button
                title="View"
                type="Secondary"
                buttonCSS
                classNames="w-full h-[24] sm:h-[32] lg:h-[32] rounded-lg mt-2"
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ImageDataCard;
