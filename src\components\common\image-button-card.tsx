'use client';

import React from "react";
import Image from "next/image";
import { ImageButtonCardProps } from "./_type";

const ImageButtonCard: React.FC<ImageButtonCardProps> = ({
  imgSrc,
  alt,
  onFirstButtonClick,
  onSecondButtonClick,
  firstButtonIcon,
  secondButtonIcon,
}) => {
  const hasFirstButton = !!firstButtonIcon;
  const hasSecondButton = !!secondButtonIcon;
  const hasBothButtons = hasFirstButton && hasSecondButton;

  return (
    <div className="relative w-full h-42 sm:h-28 lg:h-40 ">
      <Image
        src={imgSrc}
        alt={alt}
        fill
        className="object-contain rounded-md"
        priority
      />
      {hasFirstButton && (
        <button
          onClick={onFirstButtonClick}
          className={`absolute bottom-2 sm:bottom-4 lg:bottom-6  ${hasBothButtons ? "left-2" : "right-2"} bg-white/60 backdrop-blur p-1 rounded-full shadow`}
        >
          {firstButtonIcon}
        </button>
      )}
      {hasSecondButton && (
        <button
          onClick={onSecondButtonClick}
          className="absolute bottom-2 sm:bottom-4 lg:bottom-6  right-2 bg-white/60 backdrop-blur p-1 rounded-full shadow-lg"
        >
          {secondButtonIcon}
        </button>
      )}
    </div>
  );
};

export default ImageButtonCard;
