import { RefObject, useEffect, useState } from "react";

const useAddAutoCloseEventForOutSideBoundary = (
  inputRef: RefObject<HTMLDivElement | null>
) => {
  const [outSideBoundary, setOutSideBoundary] = useState(false);

  useEffect(() => {
    const handleOutSideClick = (event: MouseEvent) => {
      if (
        inputRef.current &&
        !inputRef.current.contains(event.target as Node)
      ) {
        setOutSideBoundary(true);
      } else {
        setOutSideBoundary(false);
      }
    };

    if (typeof window !== "undefined") {
      document.addEventListener("mousedown", handleOutSideClick);
    }

    return () => {
      if (typeof window !== "undefined") {
        document.removeEventListener("mousedown", handleOutSideClick);
      }
    };
  }, [inputRef]);

  return { outSideBoundary };
};

export default useAddAutoCloseEventForOutSideBoundary;
