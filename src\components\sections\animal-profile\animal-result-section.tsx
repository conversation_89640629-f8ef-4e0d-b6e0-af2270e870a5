"use client";
import React from "react";
import { Table } from "@/components";
import { TrashIcon, EyeSlashIcon } from "@heroicons/react/24/outline";
import { PET_RESULT } from "@/constants/text-objects";
import {
  AnimalResultRow,
  AnimalResultColumns,
} from "@/components/common/_type";

// @todo: Replace with real data from API
const data: AnimalResultRow[] = [
  {
    id: "AN-001",
    name: "<PERSON>",
    species: "Dog",
    age: 4,
    barcode: "BC123456",
    result: "No abnormalities detected in blood sample.",
    Actions: null,
  },
  {
    id: "AN-002",
    name: "<PERSON>",
    species: "<PERSON>",
    age: 2,
    barcode: "BC654321",
    result: "Elevated liver enzymes observed.",
    Actions: null,
  },
  {
    id: "AN-003",
    name: "<PERSON>",
    species: "<PERSON>",
    age: 3,
    barcode: "BC789012",
    result: "Positive for parasitic infection (Giardia).",
    Actions: null,
  },
  {
    id: "AN-004",
    name: "<PERSON>",
    species: "Rabbit",
    age: 1,
    barcode: "BC890123",
    result: "Complete health panel within normal ranges.",
    Actions: null,
  },
  {
    id: "AN-005",
    name: "<PERSON>",
    species: "<PERSON>",
    age: 5,
    barcode: "BC321098",
    result: "Minor signs of inflammation in digestive tract.",
    Actions: null,
  },
];

const AnimalResultSection: React.FC = () => {
  const renderCell = (
    column: string,
    row: AnimalResultRow,
    rowIndex: number
  ) => {
    if (column === "") {
      const selected = selectedRows[rowIndex];
      return (
        selectable && (
          <input
            type="checkbox"
            checked={!!selected}
            onChange={() => handleSelectChange(rowIndex)}
            className="cursor-pointer w-5 h-5"
          />
        )
      );
    }
    if (column === "Actions") {
      return (
        <div className="flex gap-2">
          <TrashIcon className="w-5 h-5 text-gray-600 cursor-pointer" />
          <EyeSlashIcon className="w-5 h-5 text-gray-600 cursor-pointer" />
        </div>
      );
    }
    return row[column as keyof AnimalResultRow];
  };

  const [selectedRows, setSelectedRows] = React.useState<
    Record<number, boolean>
  >({});
  const [selectable, setSelectable] = React.useState(true);
  {
    /* @todo: Replace with real data from API */
  }
  setSelectable(true);
  const handleSelectChange = (index: number) => {
    setSelectedRows((prev) => ({
      ...prev,
      [index]: !prev[index],
    }));
  };
  return (
    <div className="flex flex-col p-4 border border-(--sky-slate-dark2) rounded-lg mt-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
        <div className="text-lg font-semibold">{PET_RESULT}</div>
      </div>
      <div className="mt-6 w-full">
        <Table
          columns={["", ...AnimalResultColumns]}
          data={data}
          renderCell={renderCell}
          selectable={selectable}
        />
      </div>
    </div>
  );
};

export default AnimalResultSection;
