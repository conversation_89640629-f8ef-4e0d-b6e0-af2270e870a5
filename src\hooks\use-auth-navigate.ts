import {
  getAuthCookie,
  getAccessCookie,
  getRefreshCookie,
} from "@/tools/cookies";
import { usePathname, useRouter } from "next/navigation";
import { useEffect, useState } from "react";

const useAuthNavigate = () => {
  const pathname = usePathname();
  const [isAuth, setIsAuth] = useState(
    !!(
      typeof window !== "undefined" &&
      localStorage.getItem("status") === "login"
    )
  );
  const navigate = useRouter();

  useEffect(() => {
    if (
      !!(
        typeof window !== "undefined" &&
        localStorage.getItem("status") === "login"
      )
    ) {
      setIsAuth(true);
    } else {
      const isAuthCheck = async () => {
        const authdata = await getAuthCookie();
        const refreshToken = await getRefreshCookie();
        const accessToken = await getAccessCookie();
        if (authdata && (refreshToken || accessToken)) {
          setIsAuth(true);
        } else {
          setIsAuth(false);
        }
      };

      isAuthCheck();
    }
  }, [pathname]);

  const callProtectedRouteNavigate = async (
    getUrl: (userID: string) => string
  ) => {
    const authdata = await getAuthCookie();
    const refreshToken = await getRefreshCookie();
    const accessToken = await getAccessCookie();
    const isAuth = authdata && (refreshToken || accessToken);

    if (isAuth) {
      navigate.push(getUrl(authdata.user.id.toLocaleString()));
    }
  };

  return { callProtectedRouteNavigate, isAuth };
};

export default useAuthNavigate;
