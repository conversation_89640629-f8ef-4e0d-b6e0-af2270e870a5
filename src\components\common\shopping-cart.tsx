import { ShoppingCartProps } from "./_type";
import Heading from "./heading";
import { ShoppingCartIcon } from "@heroicons/react/24/outline";

const ShoppingCart = ({
  title,
  cartIcon,
  cartItems,
  currency = "AUD",
}: ShoppingCartProps) => {
  return (
    <div className="flex flex-col flex-1  pt-6 md:pt-0 md:pl-6 gap-10">
      <div className="flex gap-2 bg-(--sky-slate) rounded-4xl p-1 w-full pr-4">
        <div className="p-3  rounded-full bg-white items-center">
          {cartIcon ?? (
            <ShoppingCartIcon
              width={26}
              height={26}
              // fill="var(--violet)"
              color="var(--violet)"
            />
          )}
        </div>
        <Heading
          lowContrast
          title={title ?? "Checkout"}
          type="Hx1"
          classNames="flex items-center"
        />
      </div>
      <div className="flex flex-col gap-2">
        {cartItems.map(({ title, value }, index) => (
          <div className="grid grid-cols-3" key={index}>
            <div className="col-span-2 flex items-center ">
              <Heading
                type="Hx2"
                title={title}
                classNames="text-(--black)! "
                lowContrast
              />
            </div>
            <div className="flex justify-end">
              <Heading
                type="Hx1"
                title={`${value.toLocaleString()} ${currency}`}
                lowContrast
              />
            </div>
          </div>
        ))}
      </div>
      <div className="border-t-1 border-(--sky-slate) grid grid-cols-3 pt-2">
        <Heading type="Hx1" title="Total" classNames="col-span-2" lowContrast />
        <Heading
          type="Hx1"
          title={`${cartItems
            .map((item) => item.value)
            .reduce((accumulate, current) => accumulate + current)
            .toLocaleString()} ${currency}`}
          classNames="flex justify-end"
          lowContrast
        />
      </div>
      <button className="bg-[var(--violet)] text-white py-3 px-6 rounded-4xl text-center hover:bg-opacity-90 transition">
        Request
      </button>
    </div>
  );
};

export default ShoppingCart;
