import React from "react";
import { TableProps } from "./_type";

const Table = <T,>({
  columns,
  data,
  columnStyles = [],
  renderCell,
}: TableProps<T> & { selectable?: boolean }) => {
  return (
    <div>
      {/* Table view for medium and up */}
      <div className="hidden md:block overflow-x-auto">
        <table className="table-auto w-full border-collapse">
          <thead>
            <tr>
              {columns.map((col, index) => (
                <th
                  key={index}
                  className={`text-left px-4 py-2 font-semibold border-b border-gray-200 ${
                    columnStyles[index] || ""
                  }`}
                >
                  {String(col)} {/* <-- just this, no nested <th> */}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {data.map((row, rowIndex) => (
              <tr key={rowIndex} className="hover:bg-gray-50">
                {columns.map((col, colIndex) => (
                  <td
                    key={colIndex}
                    className={`px-4 py-2 border-b border-gray-200 ${
                      columnStyles[colIndex] || ""
                    }`}
                  >
                    {renderCell
                      ? renderCell(col, row, rowIndex)
                      : (row as Record<string, React.ReactNode>)[col]}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Card view for small screens */}
      <div className="block md:hidden space-y-4">
        {data.map((row, rowIndex) => (
          <div key={rowIndex} className="border border-gray-300 rounded-lg p-4">
            {columns.map((col, colIndex) => (
              <div key={colIndex} className="mb-2">
                <div className="text-xs font-semibold text-gray-500">
                  {String(col)}
                </div>
                <div className={`text-sm ${columnStyles[colIndex] || ""}`}>
                  {renderCell
                    ? renderCell(col, row, rowIndex)
                    : (row as Record<string, React.ReactNode>)[col]}
                </div>
              </div>
            ))}
          </div>
        ))}
      </div>
    </div>
  );
};

export default Table;
