import React from "react";

export type SectionContainerProps = {
  children: React.ReactNode;
  classNames?: string;
  background?: "White" | "SkySlate" | "Gray";
};

export type ContentContainerProps = {
  children: React.ReactNode;
  removeMaxWidthCap?: boolean;
  classNames?: string;
};

export type HeadingContainerProps = {
  background?: "White" | "SkySlate" | "Gray";
  src?: string;
  heading?: string;
  description?: string;
};

export type specialTouchProps = {
  description?: string;
};

export type animalDataProps = {
  id?: number;
  registrationName: string;
  registrationNumber: string;
  petName: string;
  gender: string;
  dobDay?: number;
  dobMonth?: number;
  dobYear?: number;
  colour?: string | null;
  weight?: string;
  specialTouch?: string;
  weightType?: string;
  breed?: {
    id: number;
    breed_id: number;
    name: string;
  };
  species?: {
    id: number;
    name: string;
  };
  owner?: {
    id: number;
    firstName: string;
    lastName: string;
  };
};


