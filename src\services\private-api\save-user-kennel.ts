"use server";

import server<PERSON>pi from "@/lib/server-api";
import { IUserSaveKennelResponse, SaveKennelPayload } from "../_type";
import { ApiError, ApiResponse } from "@/lib/_lib.type";

const saveUserKennels = async (
  payload: SaveKennelPayload
): Promise<ApiResponse<IUserSaveKennelResponse | ApiError>> => {
  return await serverApi({
    url: "paw-print/my/kennels/",
    method: "POST",
    isProtected: true,
    body: JSON.stringify(payload),
  });
};

export default saveUserKennels;
