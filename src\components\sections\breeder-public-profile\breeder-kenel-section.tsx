"use client";

import {
  <PERSON><PERSON><PERSON><PERSON>,
  DataContainer,
  ImageDataCard,
  PageNotFound,
} from "@/components";
import { BreederKennelSectionProps } from "@/components/common/_type";
import LoadingSpinner from "@/components/common/loading-spinner";
import { useGetUserKennel } from "@/hooks";
import { useRouter } from "next/navigation";

const BreederKennelSection = ({ id }: BreederKennelSectionProps) => {
  const router = useRouter();
  const { kennels, loading, error } = useGetUserKennel(id);
  if (loading) {
    return (
      <DataContainer background="White" classNames="relative">
        <div className="flex justify-center items-center my-20">
          <LoadingSpinner />
        </div>
      </DataContainer>
    );
  }
  if (error || !kennels.length) {
    return (
      <DataContainer background="White" classNames="relative">
        <PageNotFound />
      </DataContainer>
    );
  }

  return (
    <DataContainer  classNames="relative">
      <ContentContainer>
        <h2 className="text-xl sm:text-2xl  font-semibold ">More from the breeder</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 my-8">
          {kennels.map((kennel) => (
            <div key={kennel.kennelId}>
              <ImageDataCard
                imageSrc="/temp.jpg"
                title={kennel.name}
                description={kennel.about}
                onClick={() =>
                  router.push(`/kennel/${kennel.kennelId}`)
                }
              />
            </div>
          ))}
        </div>
      </ContentContainer>
    </DataContainer>
  );
};

export default BreederKennelSection;
