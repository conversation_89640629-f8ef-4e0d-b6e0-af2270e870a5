"use server";

import server<PERSON><PERSON> from "@/lib/server-api";
import { IRefreshTokenResponse } from "../_type";
import { ApiError, ApiResponse } from "@/lib/_lib.type";

const getNewTokens = async (
  username: string,
  refreshToken: string
): Promise<ApiResponse<IRefreshTokenResponse | ApiError>> => {
  return await serverApi({
    url: "refresh/",
    method: "POST",
    body: JSON.stringify({
      username,
      refresh_token: refreshToken,
    }),
  });
};

export default getNewTokens;
