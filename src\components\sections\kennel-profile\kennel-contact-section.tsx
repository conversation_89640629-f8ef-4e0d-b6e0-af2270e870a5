"use client";
import { useRouter } from "next/navigation";
import {
  Content<PERSON><PERSON><PERSON>,
  <PERSON>tieAnimator,
  SectionContainer,
} from "@/components";
import {
  ExtendedKennelBioDataSectionProps,
  KennelData,
  IUserProfile,
} from "@/services/_type";
import {
  PhoneIcon,
  EnvelopeIcon,
  GlobeAltIcon,
  BookOpenIcon,
  CameraIcon,
} from "@heroicons/react/24/outline";
import worldJSO<PERSON> from "../../../animations/world.json";
import {
  WHERE_TO_FIND_US,
  FACEBOOK,
  INSTAGRAM,
  MEET_THE_BREEDER,
  VISIT_US,
  VIEW_PROFILE,
} from "@/constants/text-objects";
import ContactLink from "@/components/common/contact-link";

// Type guard to check if kennel is KennelData
function isKennelData(kennel: KennelData | IUserProfile): kennel is KennelData {
  return (kennel as KennelData).contact !== undefined;
}
const KennelContactSection: React.FC<ExtendedKennelBioDataSectionProps> = ({
  kennel,
  iskennel,
}) => {
  const router = useRouter();
  if (!kennel || !isKennelData(kennel)) return null;
  const { contact, address, owner } = kennel;

  return (
    <SectionContainer>
      <ContentContainer>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <h2 className="text-2xl font-semibold mb-4">{WHERE_TO_FIND_US}</h2>
            {contact?.phone && (
              <ContactLink Icon={PhoneIcon} href={`tel:${contact.phone}`}>
                {contact.phone}
              </ContactLink>
            )}
            {contact?.email && (
              <ContactLink Icon={EnvelopeIcon} href={`mailto:${contact.email}`}>
                {contact.email}
              </ContactLink>
            )}
            {contact?.websiteUrl && (
              <ContactLink
                Icon={GlobeAltIcon}
                href={contact.websiteUrl}
                isExternal
              >
                {contact.websiteUrl}
              </ContactLink>
            )}
            {contact?.facebookUrl && (
              <ContactLink
                Icon={BookOpenIcon}
                href={contact.facebookUrl}
                isExternal
              >
                {FACEBOOK}
              </ContactLink>
            )}
            {contact?.instagramUrl && (
              <ContactLink
                Icon={CameraIcon}
                href={contact.instagramUrl}
                isExternal
              >
                {INSTAGRAM}
              </ContactLink>
            )}
          </div>
          <div className="flex items-start gap-6">
            <div className="h-74 w-74 hidden md:block">
              <LottieAnimator json={worldJSON} />
            </div>
            <div>
              <h2 className="text-2xl font-semibold text-gray-600 mb-4">
                {VISIT_US}
              </h2>
              <address className="not-italic text-lg text-gray-600 font-medium lg:w-[200px]">
                {address.addressLine1}, {address.addressLine2}, {address.city},{" "}
                {address.state}, {address.country}, {address.postalCode}.
              </address>
            </div>
          </div>
        </div>
        {iskennel && (
          <>
            <h2 className="text-2xl font-semibold mb-4 mt-8 lg:mt-2">
              {MEET_THE_BREEDER}
            </h2>
            <p className="text-lg text-gray-600 font-medium mb-2 lg:mb-6">
              Hi, I am {owner.firstName} {owner.lastName}.{" "}
              <span
                className="underline cursor-pointer text-gray-600"
                role="link"
                onClick={() => router.push(`/breeder/${owner.ownerId}`)}
              >
                {VIEW_PROFILE}
              </span>
            </p>
          </>
        )}
      </ContentContainer>
    </SectionContainer>
  );
};

export default KennelContactSection;
