"use client";

import { motion } from "framer-motion";
import { useMemo } from "react";
import { RingPulseProps } from "./_type";

const RingPulse = ({ duration = 1, size = [1, 2.5] }: RingPulseProps) => {
  const ringVariants = useMemo(
    () => ({
      animate: {
        scale: size,
        opacity: [1, 0],
        transition: {
          duration,
          repeat: Infinity,
          repeatType: "loop",
          ease: "easeOut",
          repeatDelay: 0.2,
        },
      },
    }),
    [duration, size]
  );

  return (
    <>
      <motion.div
        className="absolute inset-0 border-2 border-white rounded-full"
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        variants={ringVariants as any}
        animate="animate"
      />
      {/* <motion.div
        className="absolute inset-0 border-2 border-white rounded-full"
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        variants={ringVariants as any}
        animate="animate"
        transition={{ delay: 1 }}
      /> */}
    </>
  );
};

export default RingPulse;
