import { ApiError } from "@/lib/_lib.type";
import { apiCommonError } from "@/lib/api-common-error";
import { debugLogs } from "@/lib/mode.debug";
import { SaveKennelPayload, saveUserKennels } from "@/services";
import { userKennelStore } from "@/tools/state-lib/kennel.store";
import { useState } from "react";

const useSaveUserKennel = () => {
  const [savingKennel, setSavingKennel] = useState(false);
  const clearSavedKennelFromStore = userKennelStore(
    (kennelState) => kennelState.clearSavedKennel
  );

  const callSaveUserKennel = async (payLoad: SaveKennelPayload) => {
    setSavingKennel(true);
    try {
      const response = await saveUserKennels(payLoad);
      if (response.status === 201) {
        clearSavedKennelFromStore();
        // save message
      } else {
        apiCommonError(response.status, (response.data as ApiError).error);
      }
    } catch (err) {
      debugLogs(err);
    } finally {
      setSavingKennel(false);
    }
  };

  return { callSaveUserKennel, savingKennel };
};

export default useSaveUserKennel;
