export default class ClubEncryption {
  private static instance: ClubEncryption;
  constructor() {}

  public static getInstance(): ClubEncryption {
    if (!ClubEncryption.instance) {
      ClubEncryption.instance = new ClubEncryption();
    }
    return ClubEncryption.instance;
  }

  public encrypt(plainText: string) {
    if (
      typeof window !== "undefined" &&
      process.env.NODE_ENV !== "development"
    ) {
      return window.btoa(plainText);
    } else {
      return plainText;
    }
  }

  public decrypt(encryptText: string) {
    if (
      typeof window !== "undefined" &&
      process.env.NODE_ENV !== "development"
    ) {
      return window.atob(encryptText);
    } else {
      return encryptText;
    }
  }
}
