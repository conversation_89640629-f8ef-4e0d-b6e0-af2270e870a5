import { ApiError } from "@/lib/_lib.type";
import { apiCommonError } from "@/lib/api-common-error";
import { debugLogs } from "@/lib/mode.debug";
import {
  getUserKennels,
  IUserKennel,
  IUserKennelListResponse,
} from "@/services";
import { useEffect, useState } from "react";

const useGetKennels = (initLoading?: boolean) => {
  const [userKennelsLoading, setUserKennelsLoading] = useState(initLoading);
  const [kennelData, setKennelData] = useState<IUserKennel[]>([]);

  useEffect(() => {
    if (initLoading) {
      callGetUserKennels();
    }
  }, [initLoading]);

  const callGetUserKennels = async () => {
    setUserKennelsLoading(true);
    try {
      const kennelDataResponse = await getUserKennels();
      if (kennelDataResponse.status === 200) {
        setKennelData(
          (kennelDataResponse.data as IUserKennelListResponse).data.results
        );
      } else {
        apiCommonError(
          kennelDataResponse.status,
          (kennelDataResponse.data as ApiError).error
        );
      }
    } catch (err) {
      debugLogs(err);
    } finally {
      setUserKennelsLoading(false);
    }
  };

  return {
    callGetUserKennels,
    userKennelsLoading,
    kennelData,
  };
};

export default useGetKennels;
