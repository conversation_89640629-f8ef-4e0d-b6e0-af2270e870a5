import {
  ContentContainer,
  Heading,
  NextImagePlaceHolder,
  SectionContainer,
  Text,
} from "@/components";
import { KennelBioDataSectionProps } from "@/services/_type";

const KennelBioDataSection: React.FC<KennelBioDataSectionProps> = ({
  kennel,
}) => {
  const name =
    "name" in kennel
      ? kennel.name
      : `${kennel.user.firstName} ${kennel.user.lastName}`;
  const about = kennel.about;
  const specialTouch = kennel.specialTouch;

  return (
    <SectionContainer background="White" classNames="relative">
      <div className="flex justify-center">
        <div className="rounded-full overflow-hidden w-fit absolute -top-[70px] sm:-top-[90px] lg:-top-[110px] border-6 border-white">
          <NextImagePlaceHolder
            height={180}
            width={180}
            src="/dogs/d1.png"
            classNames="scale-125 h-[140px] w-[140px] sm:h-[180px] sm:w-[180px] lg:h-[220px] lg:w-[220px]"
          />
        </div>
      </div>
      <ContentContainer classNames="mt-[70px] sm:mt-[90px] lg:mt-[110px] flex flex-col md:flex-row gap-8">
        <div className="flex-2 flex flex-col">
          <div className="flex flex-col gap-6">
            <Heading title={name} type="Title" lowContrast />
            <Text title={about} type="T2" />
            <Text title={specialTouch} type="T3" />
          </div>
        </div>
      </ContentContainer>
    </SectionContainer>
  );
};

export default KennelBioDataSection;
