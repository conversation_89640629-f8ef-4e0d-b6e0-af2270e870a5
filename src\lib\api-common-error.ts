// import { callNotifyError } from "../theme/theme.toastify";

import { debugLogs } from "./mode.debug";

// export const apiCommonError = (message?: string) => {
//   callNotifyError(message ?? "Sorry unexpected error");
// };

export const apiCommonError = (code: number, message?: string) => {
  //   callNotifyError(message ?? "Sorry unexpected error");
  if (code === 428) {
    // error handle here///
  }
  debugLogs(message ?? "Sorry unexpected error");
};
