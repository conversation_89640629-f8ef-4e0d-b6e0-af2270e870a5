import { apiCommonError } from "@/lib/api-common-error";
import { debugLogs } from "@/lib/mode.debug";
import { credentialsSignIn, ICredentials } from "@/services";
import { useState } from "react";

const useSignIn = () => {
  const [loadingSignIn, setLoadingSignIn] = useState(false);

  const callCredentialsSignIn = async (credentials: ICredentials) => {
    setLoadingSignIn(true);
    try {
      const loginError = await credentialsSignIn(credentials);
      if (loginError) {
        apiCommonError(loginError.status, loginError.error);
      }
    } catch (err) {
      debugLogs(err);
    } finally {
      setLoadingSignIn(false);
    }
  };

  return { callCredentialsSignIn, loadingSignIn };
};

export default useSignIn;
