import React, { Dispatch, SetStateAction } from "react";

export type ButtonProps = {
  onClick?: () => void;
  disable?: boolean;
  contentEND?: boolean;
  title?: string;
  type?: "Primary" | "Secondary" | "type2Primary";
  classNames?: string;
  outLine?: boolean;
  children?: React.ReactNode;
  buttonCSS?: boolean;
  loading?: boolean;
};

export type NextImagePlaceHolderProps = {
  src: string;
  width: number;
  height: number;
  lazyLoad?: boolean;
  LCP?: boolean;
  classNames?: string;
  alt?: string;
};

export type ColorPolygonProps = {
  classNames?: string;
};

export type HeadingProps = {
  title?: string;
  type?: "Title" | "Hx1" | "Hx2" | "Hx3";
  lowContrast?: boolean;
  classNames?: string;
};

export type ImageCardProps = {
  description?: string;
  title?: string;
  imageSrc?: string;
  price?: number;
  onClick?: () => void;
};

export type ImageDataCardProps = {
  description?: string;
  title?: string;
  id?: number;
  imageSrc?: string;
  price?: number;
  onClick?: () => void;
};

export type TextProps = {
  title?: string;
  type?: "T1" | "T2" | "T3" | "T4";
  classNames?: string;
  lowContrast?: boolean;
};

export type RingPulseProps = {
  size?: number[];
  duration?: number;
};

export type AvatarProps = {
  src: string;
  alt?: string;
  size?: number;
  className?: string;
};

export type PaginationBarProps = {
  currentPage: number;
  totalPages: number;
  setCurrentPage: Dispatch<SetStateAction<number>>;
  onClickPaginationTile?: ({ page }: { page: number }) => void;
  wrapperClassNames?: string;
};

export type CartItem = {
  title: string;
  value: number;
};

export type ShoppingCartProps = {
  cartItems: CartItem[];
  title?: string;
  cartIcon?: React.ReactNode | React.ReactElement;
  currency?: "USD" | "AUD" | "LKR";
};

export type TableProps<T> = {
  columns: (keyof T | "")[]; // Add "" for optional first column like checkbox
  data: T[];
  columnStyles?: string[];
  renderCell?: (
    column: keyof T | "",
    row: T,
    rowIndex: number
  ) => React.ReactNode;
  selectable?: boolean;
};
export interface PopUpProps {
  isOpen: boolean;
  onClose: () => void;
  customStyles?: string;
  title?: string;
  content?: React.ReactNode;
  footer?: React.ReactNode;
}
export interface PopupModalProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  title?: string;
  footer?: React.ReactNode;
  customStyles?: string;
}
export interface DashboardLayoutProps {
  section_one: React.ReactNode;
  section_two: React.ReactNode;
  section_three?: React.ReactNode;
}

export interface Option {
  label: string;
  value: string;
}

export interface MultiSelectProps {
  title: string;
  options: Option[];
  onChange?: (selected: Option[]) => void;
  defaultSelected?: Option[];
}
export interface ImageCropModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (images: string[]) => void;
}

export type Area = {
  x: number;
  y: number;
  width: number;
  height: number;
};

export const MAX_IMAGES = 4;

export interface ImageButtonCardProps {
  imgSrc: string;
  alt: string;
  onFirstButtonClick?: () => void;
  onSecondButtonClick?: () => void;
  firstButtonIcon?: React.ReactNode;
  secondButtonIcon?: React.ReactNode;
}

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
}

export interface DashboardHeaderProps {
  title: string;
}

export interface TextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string;
  error?: string;
}

export type AnimalResultRow = {
  id: string;
  name: string;
  species: string;
  age: number;
  barcode: string;
  result: string;
  Actions: React.ReactNode | null;
};

export const AnimalResultColumns: (keyof AnimalResultRow)[] = [
  "id",
  "name",
  "species",
  "age",
  "barcode",
  "result",
  "Actions",
];
export type Crop = {
  x: number;
  y: number;
  width: number;
  height: number;
};

export interface ContactLinkProps {
  Icon: React.FC<React.SVGProps<SVGSVGElement>>;
  href: string;
  children: React.ReactNode;
  isExternal?: boolean;
}

export interface DropdownProps {
  options: string[];
  defaultValue?: string;
  onChange?: (value: string) => void;
  width?: string; 
}
export interface DropdownIconProps {
  isOpen: boolean;
  className?: string; 
}

export type ActionDropdownItemType = {
  id: string | number;
  name: string;
  href?: string;
  onClick?: (e: React.MouseEvent<HTMLAnchorElement | HTMLSpanElement>) => void;
};

export type ActionDropdownPropsType = {
  items: ActionDropdownItemType[];
};

export type PopUpPropsType = {
  title: string;
  description?: string;
  popUpType?: "danger" | "Warning" | "common";
  onClickPrimary?: () => void;
  onClickSecondary?: () => void;
  primaryButtonTitle?: string;
  secondaryButtonTitle?: string;
  loadingPrimary?: boolean;
};

export interface BreederKennelSectionProps {
  id: number;
}