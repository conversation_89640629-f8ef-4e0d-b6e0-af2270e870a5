@import "flowbite/dist/flowbite.min.css";
@import "tailwindcss";

body {
  font-family: var(--font-geist-poppins);
  color: var(--violet-dark);
}

:root {
  --white: "#FFFFFF";
  --full-gray: #c5c4c5;
  --gray: #fbfbfb;
  --sky-slate: #f2f5fe;
  --sky-slate-dark: #dadbde;
  --sky-slate-dark2: #eeeef9;
  --black: #381464;
  --full-black: #363843;
  --violet-light: #f0eeff;
  --violet-medium: #ab69f6b1;
  --violet-medium-light: #e6d5f9;
  --violet: #6f4bdb;
  --violet-dark: #381464;
  --violet-dark2: #290D4B;
  --sky-blue: #e0e6ff;
  --sky-orange: #f9e0ffb7;
  --orange: #fe8d4d;
  --red: #fe5f8aee;
}

.skin {
  display: none;
}
@media (min-width: 600px) {
  .skin {
    display: inline-block;
  }
}

::-webkit-scrollbar {
  background: var(--violet-medium);
  width: 6px;
}

::-webkit-scrollbar-thumb {
  background-color: #6f4bdb;
  border-radius: 8px;
  width: 8px;
  cursor: pointer;
}

/* glass effect */
.glass-ef {
  /* From https://css.glass */
  background: rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(2px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}
