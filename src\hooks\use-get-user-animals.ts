import { ApiError } from "@/lib/_lib.type";
import { apiCommonError } from "@/lib/api-common-error";
import { debugLogs } from "@/lib/mode.debug";
import {
  getUserAnimals,
} from "@/services";
import { useEffect, useState } from "react";

const useGetUserAnimals = (initLoading?: boolean) => {
  const [userAnimalsLoading, setUserAnimalLoading] = useState(initLoading);

  useEffect(() => {
    if (initLoading) {
      callUserAnimals();
    }
  }, [initLoading]);

  const callUserAnimals = async () => {
    setUserAnimalLoading(true);
    try {
      const kennelDataResponse = await getUserAnimals();
      if (kennelDataResponse.status === 200) {
        console.log(kennelDataResponse);
        // setUserAnimalData(kennelDataResponse.data.data.results);
      } else {
        apiCommonError(
          kennelDataResponse.status,
          (kennelDataResponse.data as ApiError).error
        );
      }
    } catch (err) {
      debugLogs(err);
    } finally {
      setUserAnimalLoading(false);
    }
  };

  return {
    callUserAnimals,
    userAnimalsLoading,
  };
};

export default useGetUserAnimals;
