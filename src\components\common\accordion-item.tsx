"use client";

import { useState } from "react";
import { PlusIcon, MinusIcon } from "@heroicons/react/24/solid";

const AccordionItem = ({
  title,
  subtitle,
  description,
  color,
}: {
  title: string;
  subtitle?: string;
  description: string;
  color?: string;
}) => {
  const [open, setOpen] = useState(false);

  return (
    <div className="border border-gray-200 rounded-lg mb-3 ">
      <button
        onClick={() => setOpen((prev) => !prev)}
        className="w-full flex justify-between items-center p-4 bg-white rounded-lg text-left cursor-pointer"
      >
        <div className="flex flex-col items-start">
          <h3 className="font-semibold text-sm md:text-lg">{title}</h3>
          {subtitle && (
            <p
              className={`text-md md:text-lg ${
                color ? `text-${color}-500` : "text-gray-600"
              }`}
            >
              {subtitle}
            </p>
          )}
        </div>
        <span className="ml-2 text-gray-500">
          {open ? (
            <MinusIcon width={20} height={20} className="text-black" />
          ) : (
            <PlusIcon width={20} height={20} className="text-black" />
          )}
        </span>
      </button>
      {open && (
        <div className="text-sm md:text-lg px-4 pb-4 text-gray-700 bg-white">
          {description || "No description provided."}
        </div>
      )}
    </div>
  );
};

export default AccordionItem;
