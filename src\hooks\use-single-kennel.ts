"use client";

import { useEffect, useState } from "react";
import { ApiSuccessResponse, KennelData } from "@/services/_type";
import { getKennelById } from "@/services";
import { debugLogs } from "@/lib/mode.debug";
import { isApiError } from "@/lib/api-guard";

// Type guard to check if object has 'code' property as number
const hasCode = (obj: unknown): obj is { code: number } => {
  return (
    obj !== null &&
    typeof obj === "object" &&
    "code" in obj &&
    typeof (obj as Record<string, unknown>).code === "number"
  );
};

// Check if data is valid KennelData
const isKennel = (data: unknown): data is KennelData => {
  return (
    typeof data === "object" &&
    data !== null &&
    "name" in data &&
    "kennelId" in data
  );
};

export const useSingleKennel = (kennelId: number | null) => {
  const [kennel, setKennel] = useState<KennelData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!kennelId || isNaN(kennelId)) {
      const errMsg = "[useKennel] Invalid kennelId";
      setError(errMsg);
      debugLogs(errMsg);
      setLoading(false);
      setKennel(null);
      return;
    }

    const callUseSingleKennel = async () => {
      setLoading(true);
      setError(null);
      setKennel(null);
      try {
        const response = await getKennelById(kennelId);
        const outerData = response.data;
        if (isApiError(outerData)) {
          const errMsg = outerData.error;
          setError(errMsg);
          debugLogs(errMsg);
          setLoading(false);
          return;
        }
        if (hasCode(outerData) && outerData.code === 200) {
          const successResponse = outerData as ApiSuccessResponse<unknown>;
          const innerData = successResponse.data;
          if (isKennel(innerData)) {
            setKennel(innerData);
          } else {
            const errMsg = "Invalid kennel data";
            setError(errMsg);
            debugLogs(errMsg);
            setKennel(null);
          }
        } else {
          const errMsg = "Unexpected response code or format";
          setError(errMsg);
          debugLogs(errMsg);
          setKennel(null);
        }
      } catch (e) {
        const errMsg = "Fetch error";
        setError(errMsg);
        debugLogs(e);
        setKennel(null);
      } finally {
        setLoading(false);
      }
    };

    callUseSingleKennel();
  }, [kennelId]);
  return { kennel, loading, error };
};

export default useSingleKennel;
