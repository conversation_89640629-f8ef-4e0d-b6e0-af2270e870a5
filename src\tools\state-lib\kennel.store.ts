// userKennelStore.ts

import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";
import { KennelStore } from "./_type";
import { KENNEL_STORE } from "@/constants/store-object";
import ClubEncryption from "../encryption";

export const userKennelStore = create<KennelStore>()(
  persist(
    (set) => ({
      newKennel: null,
      saveNewKennel: (newKennel) =>
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        set((prev) => ({
          newKennel: {
            ...prev.newKennel,
            ...newKennel,
          },
        })),
      clearSavedKennel: () => set({ newKennel: null }),
    }),
    {
      name: KENNEL_STORE,
      storage: createJSONStorage(() => {
        const clubEncryption = ClubEncryption.getInstance();
        return {
          getItem: (name) => {
            return clubEncryption.decrypt(localStorage.getItem(name) ?? "");
          },
          setItem: (name, value) => {
            localStorage.setItem(name, clubEncryption.encrypt(value));
          },
          removeItem: (name) => {
            localStorage.removeItem(name);
          },
        };
      }),
    }
  )
);
