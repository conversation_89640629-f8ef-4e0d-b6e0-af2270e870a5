// useBearStore.ts

import { create } from "zustand";
import { persist } from "zustand/middleware";
import { BearStore } from "./_type";

// the store itself does not need any change
export const useBearStore = create<BearStore>()(
  persist(
    (set) => ({
      bears: 1,
      addABear: (current) => set({ bears: current + 1 }),
    }),
    {
      name: "food-storage",
      // storage: createJSONStorage(() => storage),
    }
  )
);
