import { ApiError } from "@/lib/_lib.type";
import { apiCommonError } from "@/lib/api-common-error";
import { debugLogs } from "@/lib/mode.debug";
import { IKennelUpdatePayload, updateUserKennelStatus } from "@/services";
import { useState } from "react";

const useUpdateUserKennelStatus = () => {
  const [updatingKennelStatus, setUpdatingKennelStatus] = useState(false);

  const callUpdateUserKennelStatus = async (
    id: string,
    payload: IKennelUpdatePayload
  ) => {
    setUpdatingKennelStatus(true);
    try {
      const response = await updateUserKennelStatus(id, payload);
      if (response.status === 201) {
        // save message
      } else {
        apiCommonError(response.status, (response.data as ApiError).error);
      }
    } catch (err) {
      debugLogs(err);
    } finally {
      setUpdatingKennelStatus(false);
    }
  };

  return { callUpdateUserKennelStatus, updatingKennelStatus };
};

export default useUpdateUserKennelStatus;
