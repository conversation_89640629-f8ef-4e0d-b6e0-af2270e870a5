"use client";

import {
  Pa<PERSON><PERSON>,
  PaginationPrevious,
  <PERSON><PERSON>ation<PERSON>ex<PERSON>,
  <PERSON><PERSON>ationList,
  Pa<PERSON>ationPage,
  PaginationGap,
} from "./pagination";
import clsx from "clsx";
import { PaginationBarProps } from "./_type";

const PaginationBar = ({
  currentPage,
  totalPages,
  setCurrentPage,
  onClickPaginationTile,
  wrapperClassNames,
}: PaginationBarProps) => {
  const getPages = (): (number | "gap")[] => {
    const pages: (number | "gap")[] = [];
    const roundTotalPages = Math.ceil(totalPages);

    if (roundTotalPages <= 7) {
      for (let i = 1; i <= roundTotalPages; i++) {
        pages.push(i);
      }
    } else {
      pages.push(1);

      if (currentPage > 4) {
        pages.push("gap");
      }

      const start = Math.max(2, currentPage - 1);
      const end = Math.min(roundTotalPages - 1, currentPage + 1);
      for (let i = start; i <= end; i++) {
        pages.push(i);
      }

      if (currentPage < roundTotalPages - 3) {
        pages.push("gap");
      }

      pages.push(roundTotalPages);
    }

    return pages;
  };

  const pages = getPages();

  return (
    <Pagination
      className={clsx(
        wrapperClassNames,
        "border border-(--sky-slate-dark2)  rounded-4xl flex items-center! p-2 bg-(--sky-slate)"
      )}
    >
      <PaginationPrevious
        disable={currentPage === 1}
        href={currentPage > 1 ? `#` : null}
        className="cursor-pointer "
        onClick={() => {
          if (currentPage > 1) {
            setCurrentPage(currentPage - 1);
            if (onClickPaginationTile) {
              onClickPaginationTile({ page: currentPage - 1 });
            }
          }
        }}
      />

      <PaginationList className="flex items-center!">
        {pages.map((page, index) =>
          page === "gap" ? (
            <PaginationGap key={`gap-${index}`} />
          ) : (
            <PaginationPage
              key={page}
              href="#"
              className={clsx(
                currentPage === page
                  ? "text-(--primary-active-dark)!"
                  : "text-(--primary-purple)!"
              )}
              current={page === currentPage}
              onClick={() => {
                setCurrentPage(page);
                if (onClickPaginationTile) {
                  onClickPaginationTile({ page });
                }
              }}
            >
              {page}
            </PaginationPage>
          )
        )}
      </PaginationList>
      <PaginationNext
        disable={currentPage === totalPages}
        href={currentPage < totalPages ? `#` : null}
        className="cursor-pointer"
        onClick={() => {
          if (currentPage < totalPages) {
            setCurrentPage(currentPage + 1);
            if (onClickPaginationTile) {
              onClickPaginationTile({ page: currentPage + 1 });
            }
          }
        }}
      />
    </Pagination>
  );
};

export default PaginationBar;
