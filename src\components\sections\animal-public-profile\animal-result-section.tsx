"use client";

import { AccordionItem, ContentContainer, DataContainer } from "@/components";
import {
  LOADING_RESULT,
  NO_RESULT_FOUND,
  RESULTS,
  TRAITS,
  NO_TRAITS_AVAILABE,
  DISEASES,
  NO_DISEASES_AVAILABE,
} from "@/constants/text-objects";
import { useAnimalPublicResult } from "@/hooks";
import { AnimalResultViewSectionProps } from "@/services/_type";

const AnimalResultViewSection = ({
  animalId,
}: AnimalResultViewSectionProps) => {
  const { results, loading } = useAnimalPublicResult(animalId);

  if (loading) {
    return (
      <DataContainer background="Gray">
        <ContentContainer classNames="w-full">
          <div className="h-[200px] flex items-center justify-center rounded-lg">
            <p className="text-gray-500">{LOADING_RESULT}</p>
          </div>
        </ContentContainer>
      </DataContainer>
    );
  }
  if (!results) {
    return (
      <DataContainer background="Gray">
        <ContentContainer classNames="w-full">
          <div className="h-[200px] flex items-center justify-center rounded-lg">
            <p className="text-red-500">{NO_RESULT_FOUND}</p>
          </div>
        </ContentContainer>
      </DataContainer>
    );
  }

  return (
    <DataContainer background="Gray">
      <ContentContainer classNames="w-full space-y-6">
        <div>
          <div className="text-xl md:text-3xl font-semibold mb-4">
            {RESULTS}
          </div>
          <h2 className="text-md md:text-xl font-medium text-gray-800 mb-4">
            {TRAITS}
          </h2>
          {results.traits.length === 0 ? (
            <p className="text-sm md:text-md text-gray-500">
              {NO_TRAITS_AVAILABE}
            </p>
          ) : (
            results.traits.map((trait, idx) => (
              <AccordionItem
                key={idx}
                title={trait.product.name}
                description={trait.details.description}
              />
            ))
          )}
        </div>
        <div>
          <h2 className="text-md md:text-xl font-medium text-gray-800 mb-4">
            {DISEASES}
          </h2>
          {results.diseases.length === 0 ? (
            <p className="text-sm md:text-md text-gray-500">
              {NO_DISEASES_AVAILABE}
            </p>
          ) : (
            results.diseases.map((disease) => (
              <AccordionItem
                key={disease.id}
                title={disease.product.name}
                subtitle={`Results: ${disease.interpretation.interpretation}`}
                description={disease.details.description}
                color={disease.interpretation.color}
              />
            ))
          )}
        </div>
      </ContentContainer>
    </DataContainer>
  );
};

export default AnimalResultViewSection;
