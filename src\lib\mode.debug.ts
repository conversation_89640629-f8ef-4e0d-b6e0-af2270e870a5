"use server";

import { getServerConfig } from "../tools/config/protected-config";

const isDebugMode = async () => {
  const serverConfig = await getServerConfig();
  console.log({ DEBUG_MODE: serverConfig.DEBUG_MODE });
  return serverConfig.DEBUG_MODE === "dev";
};

const getDebugTrace = (): string => {
  const err = new Error();
  const stackLines = err.stack?.split("\n");

  if (stackLines && stackLines.length > 2) {
    const traceLine = stackLines[2].trim();
    return traceLine;
  }

  return "unknown location";
};

export const debugLogs = async (data: unknown) => {
  const enableLogs = await isDebugMode();
  if (enableLogs) {
    const trace = getDebugTrace();
    console.debug(`[DEBUG_MODE] ${trace}`, data);
  }
};

export default isDebugMode;
