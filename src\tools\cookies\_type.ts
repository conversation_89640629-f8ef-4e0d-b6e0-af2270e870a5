// cookies types
export interface IAccessSession {
  token: {
    expiresIn: number;
    refreshToken: string;
    accessToken: string;
    cognitoUserId: string;
  };
}

export interface IAuthSession {
  user: {
    id: number;
    username: string;
    firstName: string;
    lastName: string;
    email: string;
    address: {
      country: {
        name: string;
        code: string;
        currency: string;
        currencyCode: string;
      };
      address1: string;
      address2?: string;
    };
    collectionAgentId: string;
  };
}
