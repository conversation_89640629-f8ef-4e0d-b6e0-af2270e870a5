"use client";
import React from "react";
// import NextImagePlaceHolder from "./next-image-placeholder";

type MasonryGalleryProps = {
  images: string[];
  columns?: {
    base: number;
    md?: number;
  };
};

const MasonryGallery: React.FC<MasonryGalleryProps> = ({
  // images,
  columns = { base: 2, md: 4 },
}) => {
  const [colsCount, setColsCount] = React.useState(columns.base);
  React.useEffect(() => {
    function updateCols() {
      if (window.innerWidth >= 768) {
        setColsCount(columns.md || columns.base);
      } else {
        setColsCount(columns.base);
      }
    }
    updateCols();
    window.addEventListener("resize", updateCols);
    return () => window.removeEventListener("resize", updateCols);
  }, [columns.base, columns.md]);

  return (
    <div
      style={{
        columnCount: colsCount,
        columnGap: "12px",
      }}
      className="w-full text-2xl"
    >
      Image Gallery
      {/* {images.map((src, idx) => (
        <div
          key={idx}
          style={{
            breakInside: "avoid",
            marginBottom: "12px",
          }}
        >
          <NextImagePlaceHolder src={src} alt={`Image ${idx}`} width={200} height={200} />
         
        </div>
      ))} */}
    </div>
  );
};

export default MasonryGallery;
