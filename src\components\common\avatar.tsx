import React from "react";
import clsx from "clsx";
import Image from "next/image";
import { AvatarProps } from "./_type";

export default function Avatar({
  src,
  alt,
  size = 32,
  className,
}: AvatarProps) {
  // size is in pixels, pass directly to width and height
  const remSize = `${size * 0.25}rem`;

  return (
    <div
      className={clsx(
        "relative rounded-full overflow-hidden border",
        className
      )}
      style={{
        width: remSize,
        height: remSize,
        borderColor: "var(--sky-slate-dark2)",
      }}
    >
      <Image
        src={src}
        alt={alt || "Avatar"}
        fill
        className="object-cover rounded-full"
      />
    </div>
  );
}
