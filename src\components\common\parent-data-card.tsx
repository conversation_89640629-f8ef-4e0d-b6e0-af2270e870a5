import {
  ANIMAL_REGISTRATION_NAME,
  ANIMAL_REGISTRATION_NUMBER,
} from "@/constants/text-objects";
import {
  SpeciesIcon,
  ColorIcon,
  GenderIcon,
  BirthdayIcon,
  WeightIcon,
} from "@/icons/animal-icons";
import { ParentDataCardProps } from "@/services/_type";
import React from "react";

const ParentDataCard: React.FC<ParentDataCardProps> = ({ title, parent }) => {
  return (
    <div className="bg-white p-4 rounded-xl shadow">
      <h3 className="text-lg md:text-xl font-semibold mb-4">{title}</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
        <span className="text-sm md:text-md bg-gray-100 text-gray-600 px-2 py-1 rounded w-fit">
          {ANIMAL_REGISTRATION_NAME} {parent.registrationName} ({parent.petName}
          )
        </span>
        <span className="text-sm md:text-md bg-gray-100 text-gray-600 px-2 py-1 rounded w-fit">
          {ANIMAL_REGISTRATION_NUMBER} {parent.registrationNumber}
        </span>
      </div>
      <div className="grid grid-cols-2 md:grid-cols-3 gap-4 py-4 md:py-8 p-4">
        <div className="flex gap-2">
          <span className="text-2xl">
            <SpeciesIcon />
          </span>
          <span className="text-md md:text-lg">{parent.species?.name}</span>
        </div>
        <div className="flex gap-2">
          <span className="text-2xl">
            <ColorIcon />
          </span>
          <span className="text-md md:text-lg">{parent.colour ?? "N/A"}</span>
        </div>
        <div className="flex gap-2">
          <span className="text-2xl">
            <GenderIcon />
          </span>
          <span className="text-md md:text-lg">{parent.gender}</span>
        </div>
        <div className="flex gap-2">
          <span className="text-2xl">
            <BirthdayIcon />
          </span>
          <span className="text-md md:text-lg">
            {parent.dobDay}/{parent.dobMonth}/{parent.dobYear}
          </span>
        </div>
        <div className="flex gap-2">
          <span className="text-2xl">
            <WeightIcon />
          </span>
          <span className="text-md md:text-lg">
            {parent.weight === "0.00"
              ? "N/A"
              : `${parent.weight} ${parent.weightType}`}
          </span>
        </div>
      </div>
      {/* <div className="text-right underline mr-4">{VIEW_PROFILE}</div> */}
    </div>
  );
};

export default ParentDataCard;
