"use client";

import LottieAnimator from "@/components/common/lottie-animator";
import React from "react";
import pageNotFoundJSON from "../../../animations/page-not-found.json";

const PageNotFound = () => {
  return (
    <div className="flex flex-col items-center py-8 md:py-18 text-center">
      <div className="h-24 w-64 md:h-94 md:w-154  block">
        <LottieAnimator json={pageNotFoundJSON} />
      </div>
      <h1 className=" text-xl md:text-3xl font-bold text-gray-800 mt-10 md:mt-0">
        Page not found
      </h1>
      <p className="mt-2 text-gray-500">
        The page you’re looking for doesn’t exist or has been moved.
      </p>
    </div>
  );
};

export default PageNotFound;
