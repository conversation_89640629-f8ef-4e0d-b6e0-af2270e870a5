"use client";

import { useEffect, useState } from "react";
import { ApiSuccessResponse, KennelData } from "@/services/_type";
import { getKennelByUserId } from "@/services";
import { debugLogs } from "@/lib/mode.debug";
import { isApiError } from "@/lib/api-guard";

// ✅ Type guard to check if object has 'code' property as number
const hasCode = (obj: unknown): obj is { code: number } => {
  return (
    typeof obj === "object" &&
    obj !== null &&
    "code" in obj &&
    typeof (obj as { code?: unknown }).code === "number"
  );
};

// ✅ Type for paginated kennel response
type KennelListResponse = {
  count: number;
  next: string | null;
  previous: string | null;
  results: KennelData[];
};

// ✅ Type guard for kennel list
const isKennelList = (data: unknown): data is KennelListResponse => {
  if (typeof data !== "object" || data === null || !("results" in data)) {
    return false;
  }
  const d = data as { results?: unknown };
  return Array.isArray(d.results);
};

export const useGetUserKennel = (ownerId: number | null) => {
  const [kennels, setKennels] = useState<KennelData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!ownerId || isNaN(ownerId)) {
      const errMsg = "[useKennel] Invalid ownerId";
      setError(errMsg);
      debugLogs(errMsg);
      setLoading(false);
      setKennels([]);
      return;
    }

    const fetchKennels = async () => {
      setLoading(true);
      setError(null);
      setKennels([]);

      try {
        const response = await getKennelByUserId(ownerId);
        const outerData: unknown = response.data;

        if (isApiError(outerData)) {
          const errMsg = outerData.error;
          setError(errMsg);
          debugLogs(errMsg);
          return;
        }

        if (hasCode(outerData) && outerData.code === 200) {
          const successResponse = outerData as ApiSuccessResponse<unknown>;
          const innerData = successResponse.data;

          if (isKennelList(innerData)) {
            setKennels(innerData.results);
          } else {
            const errMsg = "Invalid kennel list data";
            setError(errMsg);
            debugLogs(errMsg);
          }
        } else {
          const errMsg = "Unexpected response code or format";
          setError(errMsg);
          debugLogs(errMsg);
        }
      } catch (e) {
        const errMsg = "Fetch error";
        setError(errMsg);
        debugLogs(e);
      } finally {
        setLoading(false);
      }
    };

    fetchKennels();
  }, [ownerId]);

  return { kennels, loading, error };
};

export default useGetUserKennel;
