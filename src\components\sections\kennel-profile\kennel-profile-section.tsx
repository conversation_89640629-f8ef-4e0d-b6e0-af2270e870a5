"use client";

import { useParams } from "next/navigation";
import {
  AnimalDataViewSection,
  KennelBioDataSection,
  AnimalPageImageSection,
  LoadingPAW,
  KennelAdvanceSection,
  KennelContactSection,
  PageNotFound,
} from "@/components";
import { useSingleKennel } from "@/hooks";

const KennelProfileSection = () => {
  const params = useParams();
  const kennelId = parseInt(params?.id as string, 10);
  const { kennel, loading, error } = useSingleKennel(kennelId);

  if (loading) {
    return (
      <div className="flex justify-center items-center my-20">
        <LoadingPAW />
      </div>
    );
  }
  if (error || !kennel) {
    return <PageNotFound />;
  }

  return (
    <div>
      <AnimalPageImageSection />
      <KennelBioDataSection kennel={kennel} />
      <AnimalDataViewSection data={kennel.animal} />
      <KennelAdvanceSection kennel={kennel} />
      <KennelContactSection kennel={kennel} iskennel />
    </div>
  );
};

export default KennelProfileSection;
