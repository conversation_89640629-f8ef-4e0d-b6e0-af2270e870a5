import { useMemo } from "react";
import { TextProps } from "./_type";

const Text = ({ type = "T1", title, classNames, lowContrast }: TextProps) => {
  const initCSS = useMemo(
    () =>
      (lowContrast ? "opacity-70\t" : "opacity-100\t").concat(
        `${classNames ?? ""}\t`
      ),
    [lowContrast, classNames]
  );

  let CSS = initCSS;

  switch (type) {
    case "T1":
      CSS += "text-base md:text-lg lg:text-xl text-(--black)";
      break;
    case "T2":
      CSS += "text-[14px] md:text-[16px] lg:text-lg text-(--black)";
      break;
    case "T3":
      CSS += "text-[12px] md:text-[14px] lg:text-base text-(--black)";
      break;
    case "T4":
      CSS += "text-[10px] md:text-[12px] text-(--black)";
      break;
    default:
      CSS += "text-[10px] md:text-[12px] text-(--black)";
  }

  return <span className={CSS}>{title}</span>;
};

export default Text;
