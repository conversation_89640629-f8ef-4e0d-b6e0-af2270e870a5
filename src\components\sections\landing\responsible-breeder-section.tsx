import {
  ContentContainer,
  Heading,
  Next<PERSON>mage<PERSON>laceHolder,
  SectionContainer,
  Text,
} from "@/components";
import {
  LANDING_RESPONSIBLE_BREED_TITLE,
  RESPONSIBLE_BREED_D1,
  RESPONSIBLE_BREED_D2,
  RESPONSIBLE_BREED_D3,
  RESPONSIBLE_BREED_D4,
  RESPONSIBLE_BREED_SUB_TITLE,
} from "@/constants/text-objects";

const ResponsibleBreederSection = () => {
  return (
    <SectionContainer background="SkySlate" classNames="flex justify-center">
      <ContentContainer classNames="flex justify-center">
        <div className="flex flex-col gap-16">
          <Heading title={LANDING_RESPONSIBLE_BREED_TITLE} type="Hx1" />
          <div className="flex gap-6 flex-col-reverse md:flex-row">
            <div className="flex-1/2 flex flex-col gap-8">
              <Text title={RESPONSIBLE_BREED_D1} type="T3" />
              <div className="flex flex-col gap-4">
                <Heading title={RESPONSIBLE_BREED_SUB_TITLE} type="Hx2" />
                <Text title={RESPONSIBLE_BREED_D2} type="T3" />
                <Text title={RESPONSIBLE_BREED_D3} type="T3" />
                <Text title={RESPONSIBLE_BREED_D4} type="T3" />
              </div>
            </div>
            <div className="flex-1/2 flex justify-center items-center">
              <NextImagePlaceHolder
                height={300}
                width={300}
                src="/landing-page/approved.svg"
              />
            </div>
          </div>
        </div>
      </ContentContainer>
    </SectionContainer>
  );
};

export default ResponsibleBreederSection;
