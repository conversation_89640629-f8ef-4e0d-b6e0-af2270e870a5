import { ApiError } from "@/lib/_lib.type";
import { apiCommonError } from "@/lib/api-common-error";
import { debugLogs } from "@/lib/mode.debug";
import {
  getUserData,
  IGetUserDataResponse,
  IUserProfilePayload,
} from "@/services";
import { useEffect, useState } from "react";
import { ProfileDataFieldObj } from "./_type";
import { userDataStore } from "@/tools/state-lib/user.store";

const useGetUserData = () => {
  const saveUserDataFromStore = userDataStore(
    (userDataState) => userDataState.userData
  );
  const saveUserDataToStore = userDataStore(
    (userDataState) => userDataState.saveUserData
  );
  const [userDataLoading, setUserDataLoading] = useState(false);
  const [userFormatData, setUserFormatData] =
    useState<ProfileDataFieldObj | null>(null);

  useEffect(() => {
    if (saveUserDataFromStore) {
      const profilePersonalInfo = [
        {
          id: "first_name",
          label: "First Name",
          placeholder: "Enter your first name",
          value: saveUserDataFromStore.user.firstName ?? "N/A",
        },
        {
          id: "last_name",
          label: "Last Name",
          placeholder: "Enter your last name",
          value: saveUserDataFromStore.user.lastName ?? "N/A",
        },
      ];
      const profileAboutFields = [
        {
          id: "about",
          label: "About",
          placeholder: "Enter your details",
          value: saveUserDataFromStore.profile.about ?? "N/A",
        },
        {
          id: "special_touch",
          label: "Additional Notes",
          placeholder: "Enter your special details",
          value: saveUserDataFromStore.profile.specialTouch ?? "N/A",
        },
      ];
      const profileContactFields = [
        {
          id: "phone",
          label: "Phone",
          placeholder: "Enter your phone number",
          value: saveUserDataFromStore.profile.phone ?? "N/A",
        },
        {
          id: "email",
          label: "Email",
          placeholder: "Enter your email",
          value: saveUserDataFromStore.profile.email ?? "N/A",
        },
        {
          id: "facebook_url",
          label: "Facebook URL",
          placeholder: "Enter your facebook URL",
          value: saveUserDataFromStore.profile.facebookUrl ?? "N/A",
        },
        {
          id: "instagram_url",
          label: "Instagram URL",
          placeholder: "Enter your instagram URL",
          value: saveUserDataFromStore.profile.instagramUrl ?? "N/A",
        },
      ];
      setUserFormatData({
        profileAboutFields,
        profileContactFields,
        profilePersonalInfo,
      });
    }
  }, [saveUserDataFromStore]);

  const callGetUserData = async () => {
    setUserDataLoading(true);
    try {
      const userDataResponse = await getUserData("GET");
      if (userDataResponse.status === 200) {
        saveUserDataToStore(
          (userDataResponse.data as IGetUserDataResponse).data
        );
      } else {
        apiCommonError(
          userDataResponse.status,
          (userDataResponse.data as ApiError).error
        );
      }
    } catch (err) {
      debugLogs(err);
    } finally {
      setUserDataLoading(false);
    }
  };

  const callSaveNewUserData = async (updateUserData: IUserProfilePayload) => {
    try {
      const userDataResponse = await getUserData("PUT", updateUserData);
      if (userDataResponse.status === 200) {
        await callGetUserData();
      } else {
        apiCommonError(
          userDataResponse.status,
          (userDataResponse.data as ApiError).error
        );
      }
    } catch (err) {
      debugLogs(err);
    }
  };

  return {
    callGetUserData,
    callSaveNewUserData,
    userDataLoading,
    saveUserDataFromStore,
    userFormatData,
  };
};

export default useGetUserData;
