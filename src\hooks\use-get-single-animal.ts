"use client";

import { useEffect, useState } from "react";
import { Animal, ApiResponseWithData } from "@/services/_type";
import { getAnimalById } from "@/services";
import { debugLogs } from "@/lib/mode.debug";
import { isApiError } from "@/lib/api-guard";

const hasCode = (obj: unknown): obj is { code: number } => {
  return (
    obj !== null &&
    typeof obj === "object" &&
    "code" in obj &&
    typeof (obj as { code?: unknown }).code === "number"
  );
};

const isAnimal = (data: unknown): data is Animal => {
  if (
    typeof data === "object" &&
    data !== null &&
    "animal" in data &&
    typeof (data as { animal?: unknown }).animal === "object" &&
    (data as { animal?: object }).animal !== null
  ) {
    const animal = (data as { animal: Record<string, unknown> }).animal;
    return "petName" in animal && typeof animal.petName === "string";
  }
  return false;
};

function isObject(value: unknown): value is Record<string, unknown> {
  return value !== null && typeof value === "object";
}

export const useGetSingleAnimal = (animalId: number | null) => {
  const [animal, setAnimal] = useState<Animal | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!animalId || isNaN(animalId)) {
      const errMsg = "Invalid animalId";
      setError(errMsg);
      debugLogs(errMsg);
      setLoading(false);
      setAnimal(null);
      return;
    }
    const callGetSingleAnimal = async () => {
      setLoading(true);
      setError(null);
      setAnimal(null);
      try {
        const response = await getAnimalById(animalId);
        const outerData =
          hasCode(response) || isApiError(response)
            ? response
            : isObject(response?.data) &&
              (hasCode(response.data) || isApiError(response.data))
            ? (response.data as ApiResponseWithData)
            : null;
        if (!outerData) {
          const errMsg = "Invalid response from server";
          setError(errMsg);
          debugLogs(errMsg);
          return;
        }
        if (isApiError(outerData)) {
          setError(outerData.error);
          setAnimal(null);
          return;
        }
        if (hasCode(outerData) && outerData.code === 200) {
          const innerData = (outerData as ApiResponseWithData).data;
          if (isAnimal(innerData)) {
            setAnimal(innerData);
          } else {
            const errMsg = "Invalid animal data";
            setError(errMsg);
            setAnimal(null);
          }
        } else {
          const errMsg = "Unexpected response code or format";
          debugLogs((outerData as { code?: unknown }).code);
          setError(errMsg);
          setAnimal(null);
        }
      } catch (e) {
        const errMsg = "Fetch error";
        debugLogs(e);
        setError(errMsg);
        setAnimal(null);
      } finally {
        setLoading(false);
      }
    };
    callGetSingleAnimal();
  }, [animalId]);
  return { animal, loading, error };
};

export default useGetSingleAnimal;
