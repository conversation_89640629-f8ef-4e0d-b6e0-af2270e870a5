import React from "react";
import { ContactLinkProps } from "./_type";

const ContactLink: React.FC<ContactLinkProps> = ({
  Icon,
  href,
  children,
  isExternal = false,
}) => (
  <div className="text-lg text-gray-600 font-medium flex items-center gap-3 mt-4">
    <Icon className="w-5 h-5" />
    <a
      href={href}
      className="hover:underline"
      {...(isExternal
        ? { target: "_blank", rel: "noopener noreferrer" }
        : {})}
    >
      {children}
    </a>
  </div>
);

export default ContactLink;
