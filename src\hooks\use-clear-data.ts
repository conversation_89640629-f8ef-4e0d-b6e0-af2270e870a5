import { clearCookies } from "@/tools/cookies";
import { useEffect } from "react";

const useClearData = (clearDataOnMount?: boolean) => {
  useEffect(() => {
    if (clearDataOnMount) {
      clearPersistData();
    }
  }, [clearDataOnMount]);

  const clearPersistData = async () => {
    await clearCookies();
    if (typeof window !== "undefined") {
      localStorage.clear();
    }
  };

  return { clearPersistData };
};

export default useClearData;
