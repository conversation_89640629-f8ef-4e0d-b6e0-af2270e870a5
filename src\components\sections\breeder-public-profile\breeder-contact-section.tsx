import { ContentContainer, SectionContainer, Text } from "@/components";
import { CONTACT_DETAILS } from "@/constants/text-objects";
import { BreederContactProps } from "@/services/_type";
import {
  PhoneIcon,
  EnvelopeIcon,
  BookOpenIcon,
  CameraIcon,
} from "@heroicons/react/24/outline";
import Link from "next/link";

const BreederContactSection: React.FC<BreederContactProps> = ({
  phone,
  email,
  facebookUrl,
  instagramUrl,
}) => {
  return (
    <SectionContainer background="White">
      <ContentContainer classNames="flex flex-col gap-4">
        <div className="text-xl sm:text-2xl font-semibold">{CONTACT_DETAILS}</div>
        <div className="flex gap-6 flex-wrap items-center">
          <div className="flex items-center gap-2">
            <PhoneIcon className="w-5 h-5 text-gray-700" />
            <Text title={phone} type="T2" />
          </div>
          <div className="flex items-center gap-2">
            <EnvelopeIcon className="w-5 h-5 text-gray-700" />
            <Text title={email} type="T2" />
          </div>
          {facebookUrl && (
            <div className="flex items-center gap-2">
              <BookOpenIcon className="w-5 h-5 text-gray-700" />
              <Link
                href={facebookUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="hover:underline"
              >
                <Text title="Facebook" type="T2" />
              </Link>
            </div>
          )}
          {instagramUrl && (
            <div className="flex items-center gap-2">
              <CameraIcon className="w-5 h-5 text-gray-700" />
              <Link
                href={instagramUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="hover:underline"
              >
                <Text title="Instagram" type="T2" />
              </Link>
            </div>
          )}
        </div>
      </ContentContainer>
    </SectionContainer>
  );
};

export default BreederContactSection;
