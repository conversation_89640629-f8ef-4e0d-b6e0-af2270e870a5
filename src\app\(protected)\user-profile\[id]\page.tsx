"use client";

import {
  ProfileImageSection,
  ProfileBioSection,
  DashboardLayout,
  DashboardHeader,
  SectionContainer,
  ContentContainer,
  ProfileKennelSection,
  LoadingPAW,
} from "@/components";
import { useGetKennels, useGetUserData } from "@/hooks";

const UserProfile = () => {
  const {
    saveUserDataFromStore: userData,
    callSaveNewUserData,
    userDataLoading,
    userFormatData,
  } = useGetUserData();
  const { kennelData, callGetUserKennels, userKennelsLoading } =
    useGetKennels(true);

  return (
    <SectionContainer classNames="min-h-[calc(100svh_-_112px)] h-fit w-full">
      <ContentContainer classNames="relative z-20 h-full">
        <DashboardHeader title="Profile" />
        {userDataLoading ? (
          <div className="flex justify-center items-center">
            <LoadingPAW />
          </div>
        ) : (
          <DashboardLayout
            section_one={
              <ProfileImageSection
                firstName={userData?.user.firstName ?? ""}
                lastName={userData?.user.lastName ?? ""}
              />
            }
            section_two={
              <ProfileBioSection
                userData={userFormatData}
                updateMethod={callSaveNewUserData}
              />
            }
          />
        )}
        <ProfileKennelSection
          kennels={kennelData}
          isLoadingKennels={userKennelsLoading}
          updataMethod={callGetUserKennels}
        />
      </ContentContainer>
    </SectionContainer>
  );
};

export default UserProfile;
