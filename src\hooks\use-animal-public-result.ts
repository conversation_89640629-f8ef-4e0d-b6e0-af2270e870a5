"use client";

import { useEffect, useState, useCallback } from "react";
import { AnimalResultsData, ApiSuccessResponse } from "@/services/_type";
import { getResultsByAnimalId } from "@/services";
import { debugLogs } from "@/lib/mode.debug";
import { isApiError } from "@/lib/api-guard";

// Type guard to check for 'code' property
const hasCode = (obj: unknown): obj is { code: number } => {
  return (
    obj !== null &&
    typeof obj === "object" &&
    "code" in obj &&
    typeof (obj as Record<string, unknown>).code === "number"
  );
};

// Type guard to check for valid AnimalResultsData shape
const isAnimalResultsData = (data: unknown): data is AnimalResultsData => {
  return typeof data === "object" && data !== null && "traits" in data;
};

export const useAnimalPublicResult = (animalId: number | null) => {
  const [results, setResults] = useState<AnimalResultsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const callAnimalPublicResult = useCallback(async (id: number) => {
    setLoading(true);
    setError(null);
    setResults(null);
    try {
      const response = await getResultsByAnimalId(id);
      const outerData = response.data;

      if (isApiError(outerData)) {
        setError(outerData.error);
        debugLogs(outerData.error);
        return;
      }

      if (hasCode(outerData) && outerData.code === 200) {
        const successResponse = outerData as ApiSuccessResponse<unknown>;
        const innerData = successResponse.data;

        if (isAnimalResultsData(innerData)) {
          setResults(innerData);
        } else {
          setError("Invalid results data");
          debugLogs("Invalid results data");
        }
      } else {
        setError("Unexpected response code or format");
        debugLogs("Unexpected response code or format");
      }
    } catch (e) {
      setError("Fetch error");
      debugLogs(e);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    if (!animalId || isNaN(animalId)) {
      setError("Invalid animalId");
      debugLogs("Invalid animalId");
      setLoading(false);
      setResults(null);
      return;
    }
    callAnimalPublicResult(animalId);
  }, [animalId, callAnimalPublicResult]);

  return { results, loading, error };
};

export default useAnimalPublicResult;
