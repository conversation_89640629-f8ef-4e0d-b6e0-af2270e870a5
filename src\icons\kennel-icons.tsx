export const IncludedIcon = () => {
  return (
    <svg
      width="52"
      height="52"
      viewBox="0 0 52 52"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M24.6667 50C22.6065 50 20.637 49.208 16.7006 47.624C6.90103 43.6784 2 41.7032 2 38.384V14M24.6667 50V24.452M24.6667 50C26.4926 50 27.9231 49.376 30.9629 48.1352M47.3333 14V23.6M9.55555 26L14.5926 28.4M37.2592 6.8L12.0741 18.8M15.4136 20.4584L8.05703 17.0672C4.01985 15.2048 2 14.2736 2 12.8C2 11.3264 4.01985 10.3952 8.05703 8.5328L15.4111 5.1416C19.957 3.0464 22.2237 2 24.6667 2C27.1096 2 29.3788 3.0464 33.9197 5.1416L41.2763 8.5328C45.3135 10.3952 47.3333 11.3264 47.3333 12.8C47.3333 14.2736 45.3135 15.2048 41.2763 17.0672L33.9222 20.4584C29.3763 22.5536 27.1096 23.6 24.6667 23.6C22.2237 23.6 19.9545 22.5536 15.4136 20.4584Z"
        stroke="black"
        strokeWidth="2.16667"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M45.7024 44.0134C45.9168 45.2048 44.9877 46.3023 43.7333 46.3376C43.4314 46.3457 42.9599 46.2234 42.0159 45.9779C41.6597 45.8802 41.3004 45.7929 40.9385 45.7162C39.879 45.5134 38.7873 45.5134 37.7278 45.7162C37.459 45.7677 37.1891 45.8374 36.6504 45.9789C35.7064 46.2234 35.2349 46.3457 34.9331 46.3376C33.6797 46.3023 32.7495 45.2048 32.9639 44.0134C33.0151 43.7264 33.2338 43.306 33.6712 42.4622L34.6088 40.6543C36.2995 37.3922 37.1454 35.7612 38.3635 35.3721C38.9929 35.1714 39.6744 35.1714 40.3039 35.3721C41.5199 35.7612 42.3668 37.3922 44.0586 40.6543"
        stroke="#6F4BDB"
        strokeOpacity="0.9"
        strokeWidth="2.16667"
        strokeLinecap="round"
      />
      <path
        d="M33.0879 30.7013C33.5274 32.3495 34.769 33.447 35.8613 33.1529C36.9547 32.8579 37.4848 31.2834 37.0454 29.6362C36.6059 27.988 35.3642 26.8905 34.272 27.1846C33.1786 27.4797 32.6484 29.0541 33.0879 30.7013ZM45.5789 30.7013C45.1394 32.3495 43.8978 33.447 42.8055 33.1529C41.7121 32.8579 41.182 31.2834 41.6215 29.6362C42.0609 27.988 43.3026 26.8905 44.3949 27.1846C45.4882 27.4797 46.0184 29.0541 45.5789 30.7013ZM28.882 37.5428C29.362 38.7605 30.4905 39.5002 31.4026 39.195C32.3146 38.8898 32.6644 37.6549 32.1844 36.4362C31.7044 35.2195 30.5759 34.4798 29.6638 34.785C28.7518 35.0902 28.4019 36.3251 28.882 37.5438V37.5428ZM49.7849 37.5428C49.3048 38.7605 48.1763 39.5002 47.2643 39.195C46.3522 38.8898 46.0024 37.6549 46.4824 36.4362C46.9624 35.2195 48.091 34.4798 49.003 34.785C49.915 35.0902 50.2649 36.3251 49.7849 37.5438V37.5428Z"
        stroke="#6F4BDB"
        strokeOpacity="0.9"
        strokeWidth="2.16667"
      />
    </svg>
  );
};

export const BehaviourIcon = () => (
  <svg
    width="48"
    height="54"
    viewBox="0 0 48 54"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M21.287 29.666C19.0291 17.7903 17.1242 18.5691 13.586 18.2417C5.08274 17.5144 1.23466 17.1051 0 20.3745C0.245683 23.6503 1.09515 26.8563 2.50774 29.839C4.97706 33.934 10.2688 32.304 28.3707 31.6445C28.6166 31.6389 28.8501 31.5384 29.0199 31.3652C29.1898 31.1919 29.282 30.9601 29.2762 30.7207C29.2705 30.4813 29.1673 30.2539 28.9894 30.0885C28.8114 29.9232 28.5733 29.8334 28.3274 29.839C25.9806 29.8343 23.6386 29.7502 21.287 29.666ZM19.202 29.5935C18.5687 26.7432 17.5298 23.9929 16.1154 21.4223C12.0011 20.7884 7.82882 20.5869 3.67034 20.8212C3.19473 20.8212 3.05301 20.8212 2.87766 21.2702C5.47908 32.1613 4.24683 29.1 19.202 29.5935Z"
      fill="#020202"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M29.2066 2.93278C30.2371 0.767201 19.423 -3.25526 17.1122 4.84346C16.6253 6.36798 16.6684 8.00629 17.2348 9.50457C17.8011 11.0029 18.8592 12.2775 20.245 13.131C21.6308 13.9845 23.2671 14.3692 24.9003 14.2256C26.5334 14.0819 28.0724 13.4179 29.2786 12.3365C33.9867 8.22279 31.0033 2.29434 29.2066 2.93278ZM28.6685 3.40051C28.5313 3.44628 28.385 3.46039 28.2412 3.44172C28.0974 3.42305 27.96 3.37212 27.8398 3.29294C24.2271 1.13437 20.6649 2.52118 19.9371 5.54739C19.6417 6.49401 19.6715 7.50861 20.022 8.43723C20.3726 9.36585 21.0247 10.1578 21.8794 10.6929C22.7341 11.228 23.7448 11.4771 24.758 11.4023C25.7713 11.3275 26.7318 10.9329 27.4939 10.2785C27.9792 9.97738 28.3979 9.58502 28.7254 9.12436C29.0529 8.66371 29.2827 8.14404 29.4013 7.59581C29.5198 7.04758 29.5248 6.48182 29.4159 5.93169C29.307 5.38157 29.0864 4.85814 28.767 4.3921C28.6563 4.25264 28.5883 4.08556 28.5709 3.91013C28.5535 3.7347 28.5873 3.55804 28.6685 3.40051ZM21.0973 18.6017C29.9776 16.0947 37.7723 27.271 33.4486 29.4249C33.2113 29.5204 33.0227 29.7038 32.9243 29.9347C32.8258 30.1657 32.8257 30.4252 32.9238 30.6562C33.0219 30.8873 33.2102 31.0709 33.4474 31.1667C33.6846 31.2626 33.9511 31.2627 34.1884 31.1672C34.8316 30.9203 35.4046 30.5269 35.8593 30.0198C36.3141 29.5128 36.6373 28.907 36.8019 28.2533C38.6539 22.3131 31.0394 15.1686 23.8884 15.8959C17.9962 16.691 19.9299 19.0343 21.0973 18.6017ZM13.0792 47.4138C13.2714 46.8478 13.4443 46.2561 13.6245 45.6504C14.6021 42.3506 15.6926 38.6672 19.8194 36.4222C20.035 36.3026 20.1954 36.1072 20.2676 35.8761C20.3398 35.645 20.3183 35.3957 20.2076 35.1795C20.0969 34.9633 19.9053 34.7966 19.6723 34.7137C19.4393 34.6308 19.1824 34.6379 18.9546 34.7337C16.6161 35.9186 14.6042 37.6333 13.0885 39.7334C11.5727 41.8335 10.5974 44.2574 10.2448 46.801C9.40406 50.2388 5.48871 51.0106 6.14447 46.6514C6.11602 44.5244 6.58631 42.4191 7.51953 40.4958C8.45276 38.5724 9.82433 36.8817 11.5299 35.5522C11.7446 35.4374 11.9036 35.2443 11.9721 35.0154C12.0406 34.7865 12.0128 34.5404 11.895 34.3314C11.7771 34.1224 11.5788 33.9675 11.3437 33.9008C11.1085 33.8342 10.8558 33.8612 10.6411 33.9759C2.18589 37.975 0.389151 54.1841 8.25828 52.5564C9.64907 52.2571 10.6267 51.6303 11.3569 50.7838C11.6147 51.4477 12.0257 52.0452 12.5582 52.5301C13.0906 53.0149 13.7304 53.3743 14.4281 53.5804C15.1257 53.7864 15.8627 53.8338 16.5821 53.7187C17.3016 53.6036 17.9842 53.3292 18.5775 52.9166C21.1357 51.2187 22.2382 48.8427 23.3168 46.5251C23.9941 45.0681 24.6619 43.6322 25.6708 42.4067C26.1103 41.8595 26.9342 41.7192 27.9023 41.5508C30.1914 41.1579 33.2757 40.627 33.893 34.2589C33.9277 34.0113 33.8617 33.7604 33.7092 33.5594C33.5566 33.3585 33.3296 33.2235 33.0763 33.1831C32.822 33.1494 32.5642 33.2136 32.3579 33.3621C32.1515 33.5106 32.0128 33.7317 31.9714 33.9783C31.0009 38.5339 29.2883 38.6976 27.3474 38.8847C26.7469 38.9409 26.1223 39.0017 25.493 39.1958C23.2351 39.8974 22.022 42.4442 20.773 45.0588C20.2373 46.179 19.6969 47.3109 19.0651 48.3165C17.9289 50.1196 16.0313 52.0677 14.2418 50.8656C12.9111 49.9699 13.0143 48.9994 13.0936 48.2346C13.1272 47.9213 13.1561 47.6383 13.0792 47.4138Z"
      fill="black"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M43.5434 33.8306C43.7911 35.6439 43.4731 37.4879 42.6306 39.1229C53.2117 49.2025 46.6469 56.0196 35.9625 53.4588L35.7199 53.3957C29.035 53.9172 22.0979 54.1253 21.1827 49.9579C21.0719 49.4435 21.0716 48.9123 21.1817 48.3978C21.2919 47.8832 21.5102 47.3964 21.8228 46.9679C22.1355 46.5395 22.5357 46.1785 22.9985 45.9079C23.4613 45.6372 23.9766 45.4627 24.5119 45.3952C24.7607 45.3755 25.0076 45.4496 25.2018 45.6022C25.396 45.7548 25.5225 45.9743 25.5552 46.2151C25.5879 46.456 25.5243 46.6999 25.3775 46.8963C25.2307 47.0928 25.012 47.2269 24.7666 47.2708C24.4255 47.3796 24.1322 47.5973 23.9341 47.8888C23.7361 48.1802 23.645 48.5282 23.6756 48.8764C23.7062 49.2245 23.8567 49.5524 24.1027 49.8069C24.3486 50.0614 24.6757 50.2275 25.0308 50.2783C25.9676 50.5519 27.4208 50.5987 29.6764 50.6712C30.5731 50.6992 31.606 50.7382 32.775 50.7881C32.5084 49.8924 32.4723 48.8119 32.4723 47.5654C32.6405 42.6987 32.6885 41.9293 32.345 41.6838C32.0616 41.478 31.5139 41.6253 30.5435 40.1403C29.3131 37.6941 29.0931 34.8818 29.9286 32.2824C30.8173 28.7043 31.9295 27.121 35.7944 30.6383C36.3773 30.5853 36.9602 30.5853 37.5431 30.6383L38.1892 30.0794C39.7025 28.7628 40.4039 28.1524 41.2494 28.9498C42.4996 30.3159 43.2989 32.0165 43.5434 33.8306ZM41.4872 35.0397C41.5623 36.2736 41.3057 37.5052 40.7426 38.6131C40.0484 39.282 40.4039 39.6515 41.3023 40.5869C41.9028 41.2114 42.7435 42.0883 43.6803 43.4775C44.9534 45.3648 46.3682 48.419 44.6147 50.0912C43.6107 51.0547 40.5937 50.9261 39.0372 50.8583L38.8018 50.8489C37.274 50.739 36.3997 50.7787 35.9193 50.3742C35.0786 49.6585 35.4701 47.5678 35.7151 40.8676C35.7704 39.7778 35.1963 39.6141 34.5189 39.4176C33.9184 39.2469 33.2362 39.0528 32.8423 38.1664C32.2245 36.1467 32.2987 33.9861 33.0536 32.0111C33.51 32.2754 33.8295 32.5537 34.1057 32.7946C34.5862 33.2155 34.9296 33.5102 35.5974 33.3792C36.9185 33.1103 37.4638 33.2109 37.8121 33.274C38.3718 33.3769 38.4102 33.3839 40.315 31.5761C41.0092 32.6122 41.4128 33.8072 41.4872 35.0397Z"
      fill="#6F4BDB"
      fillOpacity="0.9"
    />
  </svg>
);

export const HealthIcon = () => (
  <svg
    width="48"
    height="49"
    viewBox="0 0 48 49"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M17.683 14.8477H12.631M12.631 14.8477H7.57898M12.631 14.8477V8.90906M12.631 14.8477V20.7863"
      stroke="black"
      strokeWidth="1.5"
      strokeLinecap="round"
    />
    <path
      d="M5.51003 23.8919C4.53743 22.7544 3.76685 21.4004 3.24317 19.9089C2.7195 18.4173 2.45322 16.8179 2.45984 15.2039C2.45984 11.9397 3.56127 8.80911 5.52183 6.50093C7.4824 4.19275 10.1415 2.89603 12.9141 2.89603C16.8007 2.89603 20.1953 5.38657 21.9909 9.09342H24.746C25.6588 7.20831 26.9797 5.64167 28.5742 4.55304C30.1686 3.46441 31.9796 2.89267 33.8228 2.89603C36.5954 2.89603 39.2545 4.19275 41.2151 6.50093C43.1756 8.80911 44.2771 11.9397 44.2771 15.2039C44.2771 18.5922 43.0471 21.7199 41.2269 23.8919L23.3685 44.8877L5.51003 23.8919ZM42.9488 25.948C45.2856 23.1679 46.7369 19.4031 46.7369 15.2039C46.7369 11.1716 45.3763 7.30445 42.9544 4.45317C40.5326 1.60188 37.2478 5.22782e-05 33.8228 5.22782e-05C29.518 5.22782e-05 25.7053 2.46163 23.3685 6.28433C22.1757 4.33388 20.6051 2.74636 18.787 1.65345C16.9688 0.560536 14.9556 -0.00626778 12.9141 5.22782e-05C9.4891 5.22782e-05 6.20434 1.60188 3.78247 4.45317C1.36059 7.30445 0 11.1716 0 15.2039C0 19.4031 1.4513 23.1679 3.78815 25.948L23.3685 49L42.9488 25.948Z"
      fill="black"
    />
    <path
      d="M42.3245 26.1456L39.0767 20.725C38.836 20.3241 38.5161 19.9953 38.1434 19.7657C37.7708 19.5362 37.3562 19.4125 36.9339 19.405L30.7706 19.3029H30.7579C27.5759 19.3029 25.1344 19.9178 23.562 22.9191C22.0981 25.7127 21.4736 30.4716 21.4736 38.8311V39.8527H23.4939V44.5453L25.0749 43.0604L25.296 39.8527H25.8947C27.3388 39.8676 28.7301 39.2256 29.768 38.0654C30.7063 37.028 31.3831 35.6088 31.7254 33.9613L31.7284 33.9465L33.245 25.5498H31.4385L30.0028 33.4987C29.5656 35.5854 28.2581 37.8094 25.8947 37.8094H23.2454C23.2934 30.5772 23.8633 26.2893 25.0749 23.9769C26.0917 22.0358 27.578 21.3468 30.7517 21.3458L36.9085 21.448C37.0492 21.4505 37.1875 21.4917 37.3117 21.5682C37.4359 21.6447 37.5425 21.7543 37.6228 21.888L41.2966 28.019L46.2316 28.9694V30.0522L45.4086 35.1234C45.1657 36.6202 44.7505 37.3742 43.0478 37.6105L36.0678 38.9903L36.0216 47.515H37.79L37.8269 40.7152L43.2814 39.6361C44.5171 39.4605 45.4288 39.0004 46.0681 38.2299C46.6146 37.571 46.9473 36.7293 47.1468 35.5L48 30.2421V27.2385L42.3245 26.1456Z"
      fill="#6F4BDB"
      fillOpacity="0.9"
    />
  </svg>
);

export const EnvironmentIcon = () => (
  <svg
    width="45"
    height="46"
    viewBox="0 0 45 46"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M22.5 1.53343L23.475 0.3681C23.2033 0.130542 22.8575 0 22.5 0C22.1425 0 21.7967 0.130542 21.525 0.3681L22.5 1.53343ZM1.5 19.9334L0.525 18.7681L0 19.2281V19.9334H1.5ZM16.5 44.4667V46C16.8978 46 17.2794 45.8385 17.5607 45.5509C17.842 45.2633 18 44.8733 18 44.4667H16.5ZM28.5 44.4667H27C27 44.8733 27.158 45.2633 27.4393 45.5509C27.7206 45.8385 28.1022 46 28.5 46V44.4667ZM43.5 19.9334H45V19.2281L44.475 18.7681L43.5 19.9334ZM4.5 46H16.5V42.9333H4.5V46ZM44.475 18.7681L23.475 0.3681L21.525 2.69876L42.525 21.0987L44.475 18.7681ZM21.525 0.3681L0.525 18.7681L2.475 21.0987L23.475 2.69876L21.525 0.3681ZM18 44.4667V35.2667H15V44.4667H18ZM27 35.2667V44.4667H30V35.2667H27ZM28.5 46H40.5V42.9333H28.5V46ZM45 41.4V19.9334H42V41.4H45ZM0 19.9334V41.4H3V19.9334H0ZM22.5 30.6667C23.6935 30.6667 24.8381 31.1513 25.682 32.014C26.5259 32.8767 27 34.0467 27 35.2667H30C30 33.2334 29.2098 31.2833 27.8033 29.8455C26.3968 28.4078 24.4891 27.6 22.5 27.6V30.6667ZM22.5 27.6C20.5109 27.6 18.6032 28.4078 17.1967 29.8455C15.7902 31.2833 15 33.2334 15 35.2667H18C18 34.0467 18.4741 32.8767 19.318 32.014C20.1619 31.1513 21.3065 30.6667 22.5 30.6667V27.6ZM40.5 46C41.6935 46 42.8381 45.5154 43.682 44.6527C44.5259 43.79 45 42.62 45 41.4H42C42 41.8067 41.842 42.1967 41.5607 42.4842C41.2794 42.7718 40.8978 42.9333 40.5 42.9333V46ZM4.5 42.9333C4.10218 42.9333 3.72064 42.7718 3.43934 42.4842C3.15804 42.1967 3 41.8067 3 41.4H0C0 42.62 0.474106 43.79 1.31802 44.6527C2.16193 45.5154 3.30653 46 4.5 46V42.9333Z"
      fill="black"
    />
    <path
      d="M20.1792 22.9406L18.1424 22.9238C17.58 22.92 17.1295 23.4079 16.8482 23.9068C16.317 24.8471 15.1111 25.502 14.0491 25.4933C13.7148 25.4906 13.3842 25.4206 13.0764 25.2874C12.7685 25.1541 12.4893 24.9601 12.2547 24.7165C12.0202 24.4729 11.8349 24.1845 11.7094 23.8677C11.5838 23.5509 11.5206 23.212 11.5232 22.8702C11.5259 22.5284 11.5943 22.1905 11.7247 21.8758C11.8551 21.5611 12.0449 21.2757 12.2832 21.0359C12.7644 20.5517 13.4141 20.2827 14.0893 20.2882C13.4141 20.2826 12.7687 20.0029 12.2951 19.5109C11.8216 19.0188 11.5586 18.3546 11.5642 17.6644C11.5697 16.9741 11.8432 16.3144 12.3246 15.8303C12.8059 15.3462 13.4557 15.0774 14.131 15.0831C15.1929 15.0918 16.3883 15.7665 16.9055 16.7161C17.1781 17.2188 17.6209 17.7141 18.1834 17.7195L26.3305 17.7864C26.8929 17.7903 27.3435 17.3023 27.6247 16.8034C28.156 15.8631 29.3618 15.2082 30.4238 15.2169C31.0991 15.2223 31.7445 15.5018 32.2182 15.9937C32.6919 16.4856 32.955 17.1498 32.9497 17.84C32.9444 18.5302 32.671 19.1901 32.1898 19.6743C31.7086 20.1585 31.0589 20.4275 30.3836 20.422C31.0589 20.4277 31.7042 20.7073 32.1778 21.1993C32.6514 21.6914 32.9143 22.3556 32.9088 23.0459C32.9033 23.7361 32.6297 24.3958 32.1484 24.8799C31.667 25.364 31.0172 25.6328 30.342 25.6271C29.28 25.6184 28.0846 24.9438 27.5675 23.9941C27.2948 23.4914 26.852 22.9961 26.2896 22.9908L24.2528 22.974"
      stroke="#6F4BDB"
      strokeOpacity="0.9"
      strokeWidth="2.28947"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const InPersonPickupIcon = () => (
  <svg
    width="45"
    height="50"
    viewBox="0 0 45 50"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M5.65016 21.8803C5.01534 21.6289 4.32472 21.5391 3.64396 21.6193C2.9632 21.6995 2.31493 21.9472 1.76079 22.3386C1.35606 22.626 1.01285 22.9869 0.750282 23.4012C0.404978 23.9695 0.158043 24.5888 0.0192776 25.2345C-0.0206199 25.3864 0.00192425 25.5473 0.0821524 25.6834C0.16238 25.8194 0.294017 25.9199 0.44928 25.9637C0.522416 25.9936 0.601108 26.0087 0.68049 26.008C0.759871 26.0072 0.838257 25.9908 0.9108 25.9595C0.983343 25.9283 1.0485 25.8829 1.10225 25.8263C1.156 25.7697 1.19719 25.703 1.22328 25.6304C1.36909 25.2178 1.55632 24.82 1.78229 24.4428C1.97497 24.1117 2.25751 23.8379 2.59929 23.6512C2.94703 23.4629 3.33753 23.3612 3.7358 23.3552C4.13407 23.3491 4.52768 23.4389 4.88135 23.6164C5.23503 23.794 5.53772 24.0537 5.76233 24.3725C5.98693 24.6913 6.12643 25.059 6.16831 25.4429C6.31452 26.6873 6.38618 27.9367 6.38332 29.1909C6.38332 31.1492 6.38332 33.1076 6.27582 35.0014L6.53167 47.2662C6.5606 47.4254 6.64651 47.5697 6.7743 47.6736C6.90209 47.7775 7.06359 47.8345 7.23042 47.8345C7.39726 47.8345 7.55876 47.7775 7.68655 47.6736C7.81434 47.5697 7.90024 47.4254 7.92918 47.2662L8.59568 35.0431C8.59568 33.1264 8.78918 31.1055 8.72468 29.1263C8.68156 27.7621 8.5161 26.4042 8.23018 25.0679C8.11546 24.3678 7.81831 23.7076 7.36684 23.1498C6.91537 22.592 6.32452 22.1551 5.65016 21.8803Z"
      fill="black"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M12.2563 15.2968C13.2631 14.7063 14.3583 14.2706 15.5028 14.0051C16.6654 13.7291 17.8541 13.5685 19.0503 13.5259C21.4151 13.3146 23.7889 13.8244 25.8401 14.9843C26.4034 15.4009 26.8721 15.9259 27.2161 16.526C27.8553 17.5204 28.4358 18.5475 28.9576 19.6073C28.9926 19.6906 29.0447 19.7662 29.1108 19.8296C29.1768 19.8929 29.2554 19.9426 29.3418 19.9757C29.4282 20.0088 29.5206 20.0246 29.6134 20.0221C29.7063 20.0197 29.7977 19.999 29.8821 19.9614C30.052 19.8883 30.1855 19.7534 30.2538 19.5859C30.3222 19.4184 30.3199 19.2315 30.2476 19.0656C29.9251 18.2947 29.3661 16.9822 28.6996 15.6697C28.2767 14.8347 27.6929 14.0858 26.9796 13.4634C25.7731 12.6173 24.4072 12.0086 22.959 11.6717C23.4455 11.2994 23.8921 10.8806 24.292 10.4217C24.636 10.0356 24.9371 9.61957 25.1951 9.17373C25.4559 8.73622 25.6709 8.27788 25.8401 7.7987C26.0923 7.04982 26.1849 6.25907 26.1121 5.47447C26.0393 4.68986 25.8027 3.92778 25.4166 3.23451C25.0306 2.54124 24.5031 1.93125 23.8662 1.44159C23.2294 0.95193 22.4965 0.592815 21.712 0.386059C20.4409 -0.0479842 19.0675 -0.117682 17.7565 0.185315C16.4455 0.488312 15.2524 1.15115 14.3203 2.09442C13.9892 2.36387 13.6953 2.66944 13.4388 3.01111C12.8312 3.84367 12.4248 4.79813 12.2499 5.80365C12.0749 6.80918 12.1359 7.83998 12.4283 8.81956C12.7853 10.0729 13.5369 11.1872 14.5783 12.0071C13.2935 12.4303 12.0935 13.0645 11.0308 13.8822C10.3039 14.4527 9.70474 15.1609 9.2699 15.9634C8.69164 17.0926 8.29296 18.3 8.0874 19.5448C8.05816 19.6242 8.0464 19.7086 8.05287 19.7927C8.05935 19.8768 8.08391 19.9586 8.12498 20.0329C8.16605 20.1073 8.22271 20.1724 8.29134 20.2243C8.35996 20.2761 8.43902 20.3135 8.52343 20.3339C8.60784 20.3544 8.69573 20.3575 8.78145 20.343C8.86718 20.3286 8.94883 20.2969 9.02116 20.25C9.0935 20.2032 9.15491 20.1422 9.20145 20.071C9.24799 19.9997 9.27863 19.9198 9.2914 19.8364C9.54726 18.926 9.90631 18.0447 10.3664 17.2114C10.8234 16.4304 11.4736 15.7718 12.2584 15.2947M14.5374 3.90279C14.6487 3.84987 14.7447 3.77115 14.8169 3.67362C15.5249 2.76791 16.5266 2.11929 17.6611 1.83209C18.7955 1.5449 19.9967 1.63583 21.0713 2.09026C21.584 2.25581 22.057 2.51982 22.462 2.8665C22.867 3.21318 23.1958 3.63542 23.4286 4.108C23.6614 4.58057 23.7935 5.09377 23.817 5.61693C23.8405 6.14009 23.7549 6.66247 23.5653 7.15286C23.4569 7.48598 23.3128 7.80719 23.1353 8.11121C22.9525 8.41232 22.7439 8.69805 22.5118 8.96539C21.8493 9.68554 21.0357 10.2602 20.1253 10.6508C19.2902 11.0416 18.3446 11.1516 17.4378 10.9633C16.6993 10.8125 16.0131 10.4804 15.445 9.99886C14.8769 9.51733 14.446 8.9025 14.1934 8.21329C13.9261 7.51336 13.8184 6.76561 13.8778 6.0215C13.9372 5.27739 14.1622 4.55457 14.5374 3.90279ZM18.8998 34.1221C18.7154 34.1381 18.5447 34.2233 18.4241 34.3595C18.3036 34.4957 18.2429 34.6722 18.2548 34.8513C18.2824 37.1677 18.1675 39.4837 17.9108 41.7869C17.8543 43.2032 17.5938 44.605 17.1368 45.9515C16.7075 46.6262 16.0884 47.1683 15.3523 47.514C15.2521 47.5757 15.1403 47.6176 15.0234 47.6373C14.9065 47.6571 14.7867 47.6543 14.6708 47.6291C14.555 47.604 14.4455 47.557 14.3484 47.4907C14.2514 47.4245 14.1688 47.3404 14.1053 47.2432C13.7876 46.7098 13.6104 46.1087 13.5893 45.4932C13.4173 44.2848 13.4603 42.9723 13.3958 42.0139C13.1378 39.5785 12.9443 37.0597 12.9443 34.5388C12.9443 32.0179 12.7938 29.4783 12.9443 26.9782C12.9451 26.8172 12.8838 26.6618 12.7724 26.5423C12.661 26.4228 12.5076 26.348 12.3423 26.3324C12.1804 26.3266 12.0226 26.3821 11.9022 26.4871C11.7818 26.5921 11.7083 26.7383 11.6973 26.8949C11.4608 29.4366 11.3103 31.9783 11.2243 34.5596C11.1383 37.143 11.2243 39.6826 11.2243 42.2223C11.2243 43.6515 11.3103 45.0751 11.4823 46.4932C11.607 47.2307 11.8779 47.937 12.2778 48.5766C12.614 49.1389 13.1382 49.5732 13.7637 49.8077C14.3892 50.0422 15.0786 50.0628 15.7178 49.8662C17.1979 49.3429 18.4292 48.3129 19.1793 46.9703C19.5857 45.7182 19.8028 44.4119 19.8243 43.0973C19.9963 40.3222 19.9963 37.5458 19.8243 34.768C19.8186 34.6621 19.7885 34.5589 19.7361 34.4658C19.6838 34.3728 19.6105 34.2923 19.5218 34.2303C19.4331 34.1684 19.3311 34.1265 19.2235 34.1077C19.1159 34.089 19.0052 34.0939 18.8998 34.1221Z"
      fill="#020202"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M44.7807 26.2488C44.4378 25.5362 43.9302 24.9096 43.2972 24.4175C42.4793 23.7828 41.601 23.2247 40.6742 22.7508C39.7118 22.179 38.7145 21.6641 37.6878 21.2091C36.9027 20.8725 36.0688 20.6546 35.2153 20.5633C32.9132 20.3457 30.6112 20.9781 28.7696 22.3342C27.8741 23.0178 27.1278 23.8672 26.5742 24.8329C26.0205 25.7986 25.6705 26.8613 25.5446 27.9593C25.4601 29.3139 25.6802 30.6702 26.1896 31.9343C25.8241 32.6635 25.4156 33.3927 24.9856 34.0177C24.4638 34.8372 23.8912 35.6219 23.2677 36.3719C22.3217 37.497 21.2897 38.4553 20.4727 39.5574C19.8506 40.39 19.3647 41.3106 19.0322 42.2866C18.6378 43.2153 18.5154 44.2314 18.6785 45.2231C18.8416 46.2148 19.2839 47.1437 19.9567 47.9076C20.6397 48.5629 21.4652 49.0619 22.3729 49.3683C23.2805 49.6747 24.2474 49.7807 25.2027 49.6784L29.3501 49.3451C29.4417 49.3401 29.5312 49.3169 29.6131 49.2768C29.695 49.2368 29.7675 49.1808 29.826 49.1123C29.8846 49.0439 29.9279 48.9645 29.9533 48.879C29.9787 48.7936 29.9856 48.7041 29.9736 48.6159C29.9571 48.4372 29.8692 48.2718 29.7286 48.155C29.588 48.0382 29.4059 47.9793 29.2211 47.9909H25.1791C24.5159 48.0145 23.8554 47.8982 23.2432 47.65C22.6311 47.4018 22.0819 47.0276 21.6337 46.5534C21.2851 46.0567 21.076 45.4807 21.027 44.8819C20.978 44.2832 21.0908 43.6824 21.3542 43.1387C21.6208 42.3679 21.9884 41.6325 22.4507 40.9512C23.2032 39.8262 24.0632 38.8699 24.8587 37.599C25.4378 36.724 25.9538 35.8143 26.4067 34.8698C26.6647 34.3719 26.8582 33.8719 27.0732 33.351C27.4696 33.8343 28.0257 34.1706 28.6478 34.3034C29.27 34.4362 29.9203 34.3574 30.4896 34.0802C30.9721 33.8875 31.4104 33.6042 31.7794 33.2467C32.1484 32.8891 32.4408 32.4644 32.6396 31.9968C33.1212 30.7468 33.3255 29.4135 33.2416 28.0822C33.2411 27.9696 33.2121 27.8589 33.1571 27.7597C33.1021 27.6606 33.0229 27.576 32.9263 27.5135C32.8297 27.4509 32.7187 27.4123 32.6031 27.4011C32.4875 27.3898 32.3709 27.4062 32.2634 27.4488C32.1354 27.5009 32.0267 27.5893 31.9515 27.7023C31.8762 27.8153 31.8381 27.9477 31.842 28.0822C31.872 29.2047 31.652 30.3204 31.197 31.3531C30.939 31.9156 30.4595 32.3594 29.8639 32.5823C29.5746 32.6992 29.2505 32.7062 28.956 32.602C28.6616 32.4977 28.4185 32.2899 28.2751 32.0198C27.6987 30.8689 27.482 29.58 27.6516 28.3114C27.773 27.4855 28.0639 26.6915 28.507 25.9762C28.9501 25.261 29.5365 24.6389 30.2316 24.1467C31.6177 23.1778 33.3247 22.74 35.024 22.9175C35.6311 22.9885 36.2247 23.1429 36.787 23.3758C37.7445 23.7842 38.6754 24.2425 39.5799 24.7509C40.4169 25.1162 41.2124 25.5537 41.9664 26.0634C42.3663 26.3551 42.7017 26.7238 42.9554 27.1468C43.1193 27.6199 43.1193 28.132 42.9554 28.6051C42.5254 30.1031 41.4289 30.0197 41.4289 30.0197C41.0204 30.0406 40.6119 30.0406 40.2034 30.0197C39.1499 30.0197 38.0533 29.8114 37.0643 29.7906H34.5058C34.4241 29.7961 34.3442 29.8172 34.2709 29.8527C34.1976 29.8882 34.1322 29.9373 34.0785 29.9973C34.0247 30.0573 33.9838 30.1269 33.9578 30.2023C33.9319 30.2776 33.9216 30.3572 33.9275 30.4364C33.9302 30.5149 33.9492 30.592 33.9833 30.6632C34.0175 30.7344 34.0662 30.7981 34.1264 30.8507C34.1867 30.9032 34.2572 30.9434 34.3338 30.9689C34.4105 30.9943 34.4917 31.0046 34.5725 30.9989H37.4105C37.4105 33.4156 37.1955 35.8094 37.2385 38.2241C37.2385 39.6616 37.3675 41.1199 37.475 42.5575C37.647 44.6388 37.8835 46.8888 38.034 49.0534C38.0311 49.1427 38.0468 49.2317 38.0801 49.315C38.1133 49.3983 38.1636 49.4742 38.2277 49.5384C38.2919 49.6025 38.3687 49.6535 38.4536 49.6883C38.5386 49.7231 38.6298 49.741 38.722 49.7409C38.9098 49.7404 39.0897 49.6677 39.2223 49.5388C39.3549 49.41 39.4294 49.2354 39.4294 49.0534C39.5799 46.9701 39.6874 44.8867 39.7089 42.6596V39.9095C39.7089 38.9741 39.7089 38.099 39.5154 37.2032C39.3219 35.1198 38.9994 33.1844 38.7199 31.1656L40.1389 31.3531C40.6262 31.3836 41.1135 31.3836 41.6009 31.3531C41.9793 31.3711 42.3591 31.3711 42.7404 31.3531C43.0031 31.3323 43.2519 31.23 43.4499 31.0614C44.0992 30.4406 44.5657 29.6656 44.8022 28.8114C45.0731 27.9762 45.0655 27.0796 44.7807 26.2488Z"
      fill="#6F4BDB"
      fillOpacity="0.9"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M34.809 42.3057C34.6155 41.0765 34.3145 39.9098 34.078 38.6827C34.0753 38.6042 34.0563 38.5271 34.0221 38.4559C33.9879 38.3847 33.9392 38.3209 33.879 38.2684C33.8188 38.2159 33.7482 38.1757 33.6716 38.1502C33.5949 38.1247 33.5138 38.1145 33.433 38.1202C33.2694 38.1308 33.1162 38.2012 33.0044 38.3173C32.8926 38.4334 32.8306 38.5864 32.8309 38.7452C32.7234 39.9952 32.5944 41.2036 32.5729 42.4536C32.54 42.9744 32.54 43.4953 32.5729 44.0161C32.5729 44.5349 32.6804 45.0557 32.7449 45.5766C32.9384 46.7849 33.1749 47.9724 33.3469 49.2016C33.3469 49.384 33.4217 49.5588 33.5548 49.6878C33.6878 49.8167 33.8683 49.8891 34.0565 49.8891C34.2446 49.8891 34.4251 49.8167 34.5581 49.6878C34.6912 49.5588 34.766 49.384 34.766 49.2016C34.766 47.9724 34.9595 46.7432 34.981 45.4932V43.9307C34.9603 43.386 34.9028 42.8431 34.809 42.3057Z"
      fill="#6F4BDB"
      fillOpacity="0.9"
    />
  </svg>
);

export const GroundTransportIcon = () => (
  <svg
    width="47"
    height="42"
    viewBox="0 0 47 42"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M43.75 1H19C18.4033 1 17.831 1.26339 17.409 1.73223C16.9871 2.20107 16.75 2.83696 16.75 3.5V31C16.75 31.663 16.9871 32.2989 17.409 32.7678C17.831 33.2366 18.4033 33.5 19 33.5H43.75C44.3467 33.5 44.919 33.2366 45.341 32.7678C45.7629 32.2989 46 31.663 46 31V3.5C46 2.83696 45.7629 2.20107 45.341 1.73223C44.919 1.26339 44.3467 1 43.75 1ZM1 33.5H16.75V16H8.875L1 24.0775V33.5Z"
      stroke="black"
      strokeWidth="2"
      strokeLinejoin="round"
    />
    <path
      d="M16.75 36C16.75 37.3261 16.2759 38.5979 15.432 39.5355C14.5881 40.4732 13.4435 41 12.25 41C11.0565 41 9.91193 40.4732 9.06802 39.5355C8.22411 38.5979 7.75 37.3261 7.75 36M41.5 36C41.5 37.3261 41.0259 38.5979 40.182 39.5355C39.3381 40.4732 38.1935 41 37 41C35.8065 41 34.6619 40.4732 33.818 39.5355C32.9741 38.5979 32.5 37.3261 32.5 36"
      stroke="black"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M28.4585 9.6337C29.374 9.48714 30.3438 10.5992 30.6231 12.1337C30.9024 13.6596 30.3981 15.0216 29.4826 15.1768C28.5749 15.332 27.5973 14.2199 27.3102 12.6854C27.0231 11.1596 27.543 9.79749 28.4585 9.6337ZM34.0059 9.6337C34.9292 9.79749 35.4412 11.1596 35.1697 12.6854C34.8749 14.2199 33.905 15.332 32.9895 15.1768C32.0662 15.0216 31.5619 13.6596 31.849 12.1337C32.1283 10.5992 33.0981 9.48714 34.0059 9.6337ZM24.3076 13.5992C25.1921 13.1768 26.3947 13.944 27.0231 15.2802C27.6128 16.6423 27.4111 18.0734 26.5343 18.4958C25.6576 18.9182 24.4628 18.1596 23.8499 16.8061C23.2369 15.4527 23.4542 14.013 24.3076 13.5992ZM38.2731 13.5992C39.1266 14.013 39.3438 15.4527 38.7309 16.8061C38.118 18.1596 36.9231 18.9182 36.0464 18.4958C35.1697 18.0734 34.968 16.6423 35.5576 15.2802C36.1861 13.944 37.3886 13.1768 38.2731 13.5992ZM36.9774 22.8923C37.0085 23.7027 36.4499 24.5992 35.7826 24.9354C34.3938 25.6423 32.749 24.1768 31.205 24.1768C29.6611 24.1768 28.0007 25.7027 26.6352 24.9354C25.8593 24.513 25.324 23.3923 25.4249 22.4613C25.5645 21.1768 26.9533 20.4871 27.7757 19.5475C28.8697 18.332 29.6455 16.0475 31.205 16.0475C32.7567 16.0475 33.5792 18.2975 34.6266 19.5475C35.4878 20.5992 36.9231 21.4871 36.9774 22.8923Z"
      fill="#6F4BDB"
      fillOpacity="0.9"
    />
  </svg>
);

export const DomesticAirTravelIcon = () => (
  <svg
    width="45"
    height="46"
    viewBox="0 0 45 46"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M40.6067 28.7984C41.1781 28.9749 41.7137 29.2794 42.1831 29.6944C42.6524 30.1095 43.0461 30.6271 43.3419 31.2176C43.6377 31.8082 43.8297 32.4601 43.9069 33.1361C43.9841 33.8122 43.9451 34.4991 43.792 35.1578C43.6389 35.8164 43.3748 36.4339 43.0147 36.9749C42.6546 37.5158 42.2056 37.9698 41.6933 38.3107C41.181 38.6517 40.6155 38.873 40.029 38.962C39.4426 39.051 38.8466 39.006 38.2752 38.8295L12.783 30.9619C11.5835 30.5936 10.4532 29.9737 9.44867 29.1333L4.04305 24.6404C3.01242 23.7808 2.18318 22.6395 1.62985 21.3191C1.07652 19.9987 0.816423 18.5405 0.872939 17.0755L1.1508 9.77276C1.16062 9.5079 1.222 9.24898 1.3304 9.01518C1.4388 8.78137 1.59143 8.57867 1.777 8.42208C1.96257 8.26549 2.17632 8.15902 2.40242 8.11057C2.62853 8.06211 2.86119 8.07291 3.08318 8.14216L4.18451 8.48284C4.85504 8.69019 5.46426 9.10324 5.94732 9.67803C6.43038 10.2528 6.7692 10.9678 6.92773 11.747L8.15536 17.8093C8.28419 18.4441 8.70603 18.9449 9.25164 19.1138L19.3935 22.2469L20.6716 2.63596C20.6884 2.37653 20.7547 2.12458 20.8657 1.89808C20.9767 1.67158 21.1297 1.47612 21.3138 1.32565C21.4979 1.17518 21.7085 1.07341 21.9307 1.02761C22.1528 0.981808 22.381 0.993099 22.599 1.06068L23.6649 1.38971C24.5819 1.67215 25.2891 2.51949 25.4963 3.58811L29.7702 25.4528L40.6067 28.7984Z"
      stroke="black"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M0 45C0 44.7348 0.263392 44.4804 0.732233 44.2929C1.20107 44.1054 1.83696 44 2.5 44H42.5C43.163 44 43.7989 44.1054 44.2678 44.2929C44.7366 44.4804 45 44.7348 45 45C45 45.2652 44.7366 45.5196 44.2678 45.7071C43.7989 45.8946 43.163 46 42.5 46H2.5C1.83696 46 1.20107 45.8946 0.732233 45.7071C0.263392 45.5196 0 45.2652 0 45Z"
      fill="#7A59DB"
      fillOpacity="0.9"
    />
  </svg>
);

export const InternationalAirTravelIcon = () => (
  <svg
    width="47"
    height="47"
    viewBox="0 0 47 47"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M17.5803 22.2063C17.2459 22.8998 17.059 23.6553 17.0316 24.4249C17.0041 25.1945 17.1366 25.9614 17.4208 26.677C17.8199 27.6512 18.5009 28.4837 19.3762 29.0674C20.2514 29.6512 21.281 29.9594 22.3326 29.9527C23.041 29.9527 23.7203 30.2345 24.2213 30.7361C24.7222 31.2377 25.0036 31.918 25.0036 32.6274V44.6812M1.24231 31.3074H7.65958C8.37269 31.2889 9.08228 31.4135 9.74649 31.674C10.4107 31.9346 11.0161 32.3257 11.5269 32.8243C12.0378 33.3229 12.4437 33.9189 12.7208 34.5771C12.9979 35.2354 13.1405 35.9426 13.1403 36.6569V45.1675M45.2613 5.94941L41.7925 4.76835C41.5487 4.68541 41.2845 4.68466 41.0402 4.7662C40.796 4.84774 40.5851 5.00709 40.4397 5.21993L37.9075 9.14521L24.0323 2.19781C22.9357 1.55062 21.7098 1.15414 20.4423 1.03676C19.1748 0.91937 17.8971 1.08397 16.7006 1.51879C15.504 1.9536 14.4183 2.64788 13.5209 3.55195C12.6236 4.45601 11.9369 5.54751 11.5099 6.74836C11.3923 7.05988 11.3417 7.39281 11.3616 7.72528C11.3815 8.05775 11.4714 8.38225 11.6253 8.67748C11.7793 8.97272 11.9939 9.23201 12.2549 9.43834C12.516 9.64467 12.8177 9.79337 13.1403 9.87469L22.1938 12.7926L23.0957 13.1052L24.7954 18.9411C24.85 19.1443 24.9549 19.3304 25.1005 19.4822C25.246 19.634 25.4274 19.7466 25.6279 19.8095L29.7211 21.1295C29.9301 21.1977 30.153 21.2109 30.3686 21.1681C30.5842 21.1252 30.7851 21.0276 30.9523 20.8847C31.1194 20.7418 31.247 20.5582 31.323 20.3517C31.399 20.1452 31.4207 19.9226 31.3861 19.7053L30.4843 15.3631H31.074L39.9194 18.2463C40.4991 18.4464 41.1342 18.409 41.6865 18.1422C42.2388 17.8755 42.6634 17.401 42.8679 16.8221L45.9898 7.19994C46.0241 6.9401 45.9712 6.6762 45.8393 6.4498C45.7074 6.22339 45.5041 6.04736 45.2613 5.94941Z"
      stroke="#6F4BDB"
      strokeOpacity="0.9"
      strokeWidth="1.77545"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M35.688 28.6327C35.6878 31.5461 34.9558 34.4126 33.5593 36.9685C32.1628 39.5243 30.1468 41.6873 27.6967 43.2584C25.2467 44.8294 22.4413 45.758 19.539 45.9588C16.6366 46.1595 13.7304 45.6259 11.088 44.407C8.4455 43.1882 6.15167 41.3233 4.4176 38.9839C2.68354 36.6446 1.56496 33.906 1.16483 31.0203C0.764696 28.1346 1.09587 25.1945 2.12787 22.4705C3.15987 19.7466 4.85953 17.3263 7.0704 15.4326"
      stroke="black"
      strokeWidth="1.77545"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);




